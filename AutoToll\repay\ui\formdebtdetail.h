#ifndef FORMDEBTDETAIL_H
#define FORMDEBTDETAIL_H

#include <QLabel>
#include <QPushButton>
#include <QTimer>
#include <QPainter>
#include <QKeyEvent>
#include <QScrollBar>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>

#include "../../baseopwidget.h"
#include "../../MtcKey/MtcKeyDef.h"
#include "../../globalui.h"
#include "../common/repaytypes.h"

/**
 * @brief 补费明细显示界面
 * 用于显示省内欠费明细列表，支持滚动浏览和操作
 */
class FormDebtDetail : public CBaseOpWidget
{
    Q_OBJECT

public:
    explicit FormDebtDetail(QWidget *parent = 0);
    ~FormDebtDetail();

    // 初始化界面
    virtual void InitUI(int iFlag = 0);
    
    // 设置欠费明细数据
    void SetDebtResult(const RepayDebtQueryResult &result);
    
    // 显示明细界面
    bool ShowDebtDetail(const RepayDebtQueryResult &result);
    
    // 获取选中的明细项
    QList<RepayDebtItem> GetSelectedItems() const;
    
    // 获取总补费金额
    int GetTotalRepayAmount() const;

protected:
    // 重写绘制事件
    void paintEvent(QPaintEvent *event);
    
    // 重写按键事件处理
    virtual int mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent);
    
    // 重写模态显示后的处理
    virtual void OnModalShowed();

private slots:
    // 滚动定时器
    void OnScrollTimer();
    
    // 自动刷新定时器
    void OnRefreshTimer();

private:
    // 界面初始化
    void InitControls();
    void SetupControlProperties();
    void InitLayout();
    void InitConnections();
    void InitUIConfig();
    
    // 绘制函数
    void DrawBackground(QPainter &painter);
    void DrawTitle(QPainter &painter);
    void DrawStatistics(QPainter &painter);
    void DrawDebtListItems(QPainter &painter);
    void DrawScrollBar(QPainter &painter);
    void DrawStatusMessage(QPainter &painter);
    void DrawHelpMessage(QPainter &painter);
    void DrawDebtItem(QPainter &painter, const RepayDebtItem &item, const QRect &itemRect, bool isSelected, int itemIndex);
    
    // 数据处理
    void UpdateStatistics();
    void UpdateScrollRange();
    void FormatAmount(int amount, QString &formatted) const;
    void FormatDateTime(const QString &datetime, QString &formatted) const;
    QString GetVehicleTypeText(int vehType) const;
    QString GetPlateColorText(int plateColor) const;
    
    // 滚动处理
    void ScrollUp();
    void ScrollDown();
    void ScrollPageUp();
    void ScrollPageDown();
    void ScrollToTop();
    void ScrollToBottom();
    void ScrollToItem(int index);
    void UpdateScrollPosition();
    void CheckScrollBounds();
    
    // 选择处理
    void SelectCurrentItem();
    void SelectAllItems();
    void ClearAllSelections();
    void ToggleItemSelection(int index);
    void MoveSelectionUp();
    void MoveSelectionDown();
    void UpdateSelectionDisplay();
    
    // 输入处理
    void ProcessUpKey();
    void ProcessDownKey();
    void ProcessPageUpKey();
    void ProcessPageDownKey();
    void ProcessHomeKey();
    void ProcessEndKey();
    void ProcessSpaceKey();
    void ProcessEnterKey();
    void ProcessEscapeKey();
    void ProcessF1Key();    // 全选
    void ProcessF2Key();    // 清除选择
    void ProcessF3Key();    // 确认补费
    
    // 界面状态控制
    void SetUIEnabled(bool enabled);
    void UpdateItemsDisplay();
    void UpdateHelpMessage();
    void RefreshDisplay();
    
    // 错误处理
    void ShowErrorMessage(const QString &message);
    void ShowSuccessMessage(const QString &message);
    void ShowWarningMessage(const QString &message);
    void ClearStatusMessage();
    
    // 计算函数
    QRect GetItemRect(int index) const;
    QRect GetVisibleRect() const;
    int GetVisibleItemCount() const;
    int GetItemAtPosition(const QPoint &pos) const;
    bool IsItemVisible(int index) const;
    bool IsItemSelected(int index) const;

private:
    // 界面控件
    QLabel *m_pLblTitle;              // 标题标签
    QLabel *m_pLblStatistics;         // 统计信息标签
    QLabel *m_pLblStatus;             // 状态信息标签
    QLabel *m_pLblHelp;               // 帮助信息标签
    
    // 数据
    RepayDebtQueryResult m_debtResult;     // 欠费查询结果
    QList<bool> m_itemSelections;     // 明细项选择状态
    
    // 界面状态
    int m_nCurrentIndex;              // 当前选中项索引
    int m_nScrollOffset;              // 滚动偏移量
    int m_nItemHeight;                // 明细项高度
    int m_nVisibleItems;              // 可见明细项数量
    bool m_bAutoScroll;               // 是否自动滚动
    bool m_bMultiSelect;              // 是否多选模式
    
    // 统计信息
    int m_nTotalItems;                // 总明细数
    int m_nSelectedItems;             // 已选择明细数
    int m_nTotalAmount;               // 总欠费金额（分）
    int m_nSelectedAmount;            // 已选择金额（分）
    
    // 定时器
    QTimer *m_pScrollTimer;           // 滚动定时器
    QTimer *m_pRefreshTimer;          // 刷新定时器
    
    // 界面配置
    QFont m_fontTitle;                // 标题字体
    QFont m_fontText;                 // 普通文本字体
    QFont m_fontItem;                 // 明细项字体
    QFont m_fontStatistics;           // 统计信息字体
    
    // 颜色配置
    QColor m_colorBackground;         // 背景色
    QColor m_colorTitle;              // 标题色
    QColor m_colorText;               // 文本色
    QColor m_colorItem;               // 明细项背景色
    QColor m_colorSelectedItem;       // 选中项背景色
    QColor m_colorCurrentItem;        // 当前项背景色
    QColor m_colorBorder;             // 边框色
    QColor m_colorScrollBar;          // 滚动条色
    QColor m_colorError;              // 错误色
    QColor m_colorSuccess;            // 成功色
    QColor m_colorWarning;            // 警告色
    
    // 布局配置
    static const int TITLE_HEIGHT = 60;           // 标题高度
    static const int STATISTICS_HEIGHT = 50;      // 统计区高度
    static const int ITEM_HEIGHT = 60;            // 明细项高度（统一为较紧凑的高度）
    static const int STATUS_HEIGHT = 30;          // 状态区高度
    static const int HELP_HEIGHT = 50;            // 帮助区高度
    static const int MARGIN = 10;                 // 边距
    static const int SPACING = 5;                 // 间距
    static const int SCROLLBAR_WIDTH = 20;        // 滚动条宽度
    static const int MIN_ITEM_MARGIN = 15;        // 明细项最小边距
    
    // 滚动配置
    static const int SCROLL_STEP = 1;             // 滚动步长（行数）
    static const int SCROLL_PAGE = 5;             // 翻页步长（行数）
    static const int SCROLL_TIMER_INTERVAL = 100; // 滚动定时器间隔(ms)
    static const int REFRESH_TIMER_INTERVAL = 5000; // 刷新定时器间隔(ms)
    
    // 显示配置
    static const int MAX_STATION_NAME_LENGTH = 8;  // 站点名称最大显示长度
    static const int MAX_ORDER_ID_LENGTH = 12;     // 工单号最大显示长度
    static const int AMOUNT_DECIMAL_PLACES = 2;    // 金额小数位数
};

#endif // FORMDEBTDETAIL_H 