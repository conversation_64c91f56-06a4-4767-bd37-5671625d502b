# 江西省内补费业务配置文件模板
# 创建日期：2025年01月18日
# 说明：包含新版补费功能的所有配置项
# 使用方式：将此文件的内容添加到 lane.ini 配置文件中

[RepaySystem]
# 补费功能版本选择
UseNewRepayFunction=1          ; 1-使用新版补费功能，0-使用现有补费功能（默认1）
AllowRuntimeSwitch=1           ; 1-允许运行时切换，0-需要重启系统（默认1）
SwitchNotificationEnabled=1    ; 1-切换时显示提示信息，0-静默切换（默认1）

[ProDebtPayment]
# 省中心接口配置
BaseUrl=http://127.0.0.1:8080/ps    ; 省中心接口基础URL
TimeoutMs=10000                     ; 网络请求超时时间(毫秒)，默认10秒
RetryTimes=3                        ; 失败重试次数，默认3次
EnableAsync=1                       ; 1-启用异步请求，0-同步请求（默认1）
ConnectTimeoutMs=5000               ; 连接超时时间(毫秒)，默认5秒
ReadTimeoutMs=15000                 ; 读取超时时间(毫秒)，默认15秒

[RepayLimit]
# 各车型补费金额上限(分) - 新版补费功能专用配置
# 注意：这里的金额是以分为单位，如5000表示50元
# 优先级说明：配置文件 > 特殊参数106 > 硬编码默认值
# 如果此配置项为空或不存在，系统将自动使用现有的特殊参数106中的MaxFee配置
VehType1_MaxFee=5000               ; 一类车补费上限50元
VehType2_MaxFee=10000              ; 二类车补费上限100元
VehType3_MaxFee=15000              ; 三类车补费上限150元
VehType4_MaxFee=20000              ; 四类车补费上限200元
VehType5_MaxFee=25000              ; 五类车补费上限250元
VehType6_MaxFee=30000              ; 六类车补费上限300元
GlobalMaxFee=50000                 ; 全局补费上限500元
DailyMaxAmount=100000              ; 单车单日补费总额上限1000元

# 兼容性设置
UseSpParaMaxFee=1                  ; 1-启用特殊参数MaxFee作为后备，0-仅使用配置文件设置

[RepayAuth]
# 授权相关配置
RequireAuth=1                      ; 1-需要班长授权，0-不需要授权（默认1）
MaxRetryTimes=3                    ; 授权最大重试次数，默认3次
AuthTimeoutSecs=300                ; 授权会话超时时间(秒)，默认5分钟
AutoLogoutSecs=1800                ; 自动注销时间(秒)，默认30分钟
PasswordMinLength=6                ; 密码最小长度，默认6位
RequireManagerLevel=1              ; 1-需要班长及以上级别，0-收费员即可（默认1）

# 操作员配置说明：
# 补费系统的操作员验证使用项目现有的操作员管理功能
# 操作员信息存储在 Oper.cfg 参数文件中，由中心统一下发
# 具有班长权限(wOperType=2)或特情授权权限的操作员可以进行补费授权

# 注意：日志功能使用项目已有的日志系统，不需要单独配置

[RepayUI]
# 界面相关配置
ShowDebtDetail=1                   ; 1-显示欠费明细，0-只显示总额（默认1）
AutoRefreshInterval=5              ; 界面自动刷新间隔(秒)，默认5秒
MaxDetailItems=100                 ; 明细列表最大显示条数，默认100条
ShowProgressBar=1                  ; 1-显示操作进度条，0-不显示（默认1）
AutoCloseDelay=3                   ; 成功后自动关闭延迟(秒)，默认3秒
FontSize=12                        ; 界面字体大小，默认12
UseHighContrast=0                  ; 1-使用高对比度主题，0-默认主题（默认0）

[RepayCompatibility]
# 兼容性配置
LegacyMenuStyle=0                  ; 1-使用传统菜单样式，0-使用新菜单样式（默认0）
ShowFunctionSwitchPrompt=1         ; 1-显示功能切换提示，0-不显示（默认1）
EnableHotSwitch=1                  ; 1-启用热切换，0-禁用热切换（默认1）
BackwardCompatibilityMode=0       ; 1-向后兼容模式，0-标准模式（默认0）
LegacyDataFormat=0                 ; 1-使用旧版数据格式，0-新版格式（默认0）

[RepayNetwork]
# 网络相关配置
EnableHttps=0                      ; 1-启用HTTPS，0-使用HTTP（默认0）
EnableCompression=1                ; 1-启用数据压缩，0-不压缩（默认1）
MaxConcurrentRequests=5            ; 最大并发请求数，默认5
KeepAliveTimeout=60                ; 连接保活超时(秒)，默认60秒
UserAgent=JiangxiTollSystem/1.0    ; HTTP用户代理字符串
EnableRequestLog=1                 ; 1-记录网络请求日志，0-不记录（默认1）

[RepayPayment]
# 支付相关配置
EnableCash=1                       ; 1-支持现金支付，0-不支持（默认1）
EnableGanCard=1                    ; 1-支持赣通卡支付，0-不支持（默认1）
EnableMobilePay=1                  ; 1-支持移动支付，0-不支持（默认1）
EnableUnionPay=1                   ; 1-支持银联支付，0-不支持（默认1）
PaymentTimeoutSecs=60              ; 支付超时时间(秒)，默认1分钟
AutoSelectPayment=0                ; 1-自动选择支付方式，0-手动选择（默认0）

[RepayDebug]
# 调试相关配置（生产环境建议关闭）
EnableTestMode=0                   ; 1-启用测试模式，0-生产模式（默认0）
MockNetworkResponse=0              ; 1-模拟网络响应，0-真实请求（默认0）
DebugLevel=0                       ; 调试级别：0-关闭，1-基础，2-详细（默认0）
ShowDebugInfo=0                    ; 1-显示调试信息，0-不显示（默认0）
EnablePerformanceLog=0             ; 1-启用性能日志，0-不启用（默认0）

[RepayMaintenance]
# 系统维护配置
AutoCleanupEnabled=1               ; 1-启用自动清理，0-手动清理（默认1）
CleanupSchedule=0300               ; 自动清理时间（24小时制HHMM），默认凌晨3点
DatabaseVacuumDays=7               ; 数据库优化间隔天数，默认7天
BackupRetentionDays=30             ; 备份文件保留天数，默认30天
MaintenanceWindowStart=0200        ; 维护窗口开始时间（HHMM）
MaintenanceWindowEnd=0400          ; 维护窗口结束时间（HHMM）

# 使用说明：
# 1. 新系统部署：直接使用上述配置，UseNewRepayFunction=1
# 2. 升级部署：设置UseNewRepayFunction=0，保持现有功能不变
# 3. 功能切换：修改UseNewRepayFunction的值，如果AllowRuntimeSwitch=1则立即生效
# 4. 金额配置：所有金额都以分为单位，避免浮点数精度问题
# 5. 时间配置：超时时间建议根据网络环境调整
# 6. 日志配置：使用项目已有的日志系统，无需单独配置

# 配置文件版本标识
[RepayVersion]
ConfigVersion=1.0.0                ; 配置文件版本号
LastUpdateTime=                     ; 最后更新时间（自动维护）
UpdatedBy=                          ; 更新人员（自动维护） 