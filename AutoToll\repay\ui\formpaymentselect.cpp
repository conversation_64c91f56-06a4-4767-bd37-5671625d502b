#include "formpaymentselect.h"
#include "../../globalui.h"
#include "../../dlgmain.h"
#include "../../log4qt/ilogmsg.h"
#include <QApplication>
#include <QKeyEvent>

FormPaymentSelect::FormPaymentSelect(QWidget *parent)
    : CBaseOpWidget(parent)
    , m_pLblTitle(0)
    , m_pLblHelpInfo(0)
    , m_selectedPayType(TransPT_Cash)
    , m_selectedIndex(0)
    , m_bSelectionMade(false)
    , m_pMainLayout(0)
    , m_pOptionsLayout(0)
{
    // 初始化界面配置
    InitUIConfig();
    

    
    InfoLog("创建支付方式选择界面");
}

FormPaymentSelect::~FormPaymentSelect()
{
    InfoLog("销毁支付方式选择界面");
}

void FormPaymentSelect::InitUIConfig()
{
    // 设置字体
    m_fontTitle = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TitleFontSize, QFont::Bold);
    m_fontOption = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TextFontSize + 2);
    m_fontHelp = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TextFontSize);
    
    // 设置颜色
    m_colorBackground = g_GlobalUI.m_ColorBackground;
    m_colorTitle = QColor(0, 0, 0);
    m_colorOption = QColor(50, 50, 50);
    m_colorSelected = QColor(0, 120, 215);
    m_colorHelp = QColor(100, 100, 100);
}

void FormPaymentSelect::InitUI(int iFlag)
{
    CBaseOpWidget::InitUI(iFlag);
    
    InitControls();
    SetupControlProperties();
    InitLayout();
    InitConnections();
    // 安装子控件键盘事件过滤器
    filterChildrenKeyEvent();
    InfoLog("初始化支付方式选择界面");
}

void FormPaymentSelect::InitControls()
{
    // 创建标题标签
    m_pLblTitle = new QLabel(QString::fromUtf8("选择支付方式"), this);
    
    // 创建帮助信息标签
    m_pLblHelpInfo = new QLabel(QString::fromUtf8("请按对应数字键选择支付方式\n按【ESC】键取消"), this);
}

void FormPaymentSelect::SetupControlProperties()
{
    // 设置标题
    m_pLblTitle->setFont(m_fontTitle);
    m_pLblTitle->setAlignment(Qt::AlignCenter);
    m_pLblTitle->setText(QString::fromUtf8("选择支付方式"));
    m_pLblTitle->setStyleSheet("color: rgb(0, 0, 0);");
    
    // 设置帮助信息
    m_pLblHelpInfo->setFont(m_fontHelp);
    m_pLblHelpInfo->setAlignment(Qt::AlignCenter);
    m_pLblHelpInfo->setWordWrap(true);
    m_pLblHelpInfo->setText(QString::fromUtf8("请按对应数字键选择支付方式\n按【ESC】键取消"));
    m_pLblHelpInfo->setStyleSheet("color: rgb(100, 100, 100);");
}

void FormPaymentSelect::InitLayout()
{
    // 采用与 FormRepayTypeSelect 相同的布局方式：使用水平/垂直布局实现真正居中
    QVBoxLayout *v = new QVBoxLayout();
    v->setContentsMargins(MARGIN, MARGIN, MARGIN, MARGIN);
    v->setSpacing(20);

    // 标题置中
    v->addWidget(m_pLblTitle, 0, Qt::AlignHCenter);

    // 选项区域（中部垂直布局）
    v->addStretch();
    m_pOptionsLayout = new QVBoxLayout();
    m_pOptionsLayout->setContentsMargins(0, 0, 0, 0);
    m_pOptionsLayout->setSpacing(10);
    v->addLayout(m_pOptionsLayout);
    v->addStretch();

    // 底部帮助提示置中
    v->addWidget(m_pLblHelpInfo, 0, Qt::AlignHCenter);

    setLayout(v);
    m_pMainLayout = v;
}

void FormPaymentSelect::InitConnections()
{
    // 目前没有需要连接的信号槽
}

bool FormPaymentSelect::ShowPaymentSelect(CTransPayType &selectedPayType)
{
    InfoLog("开始支付方式选择");
    
    // 重置状态
    m_bSelectionMade = false;
    m_selectedIndex = 0;
    
    // 显示界面
    int result = doModalShow();

    if (result == CBaseOpWidget::Rlt_OK && m_bSelectionMade) {
        // 选择成功
        selectedPayType = m_selectedPayType;
        InfoLog(QString("支付方式选择成功 - 类型:%1").arg(static_cast<int>(selectedPayType)));
        return true;
    } else {
        // 选择失败或取消
        InfoLog("支付方式选择取消或失败");
        return false;
    }
}

void FormPaymentSelect::SetAvailablePayTypes(const QList<CTransPayType> &payTypes)
{
    m_availablePayTypes = payTypes;
    UpdatePaymentDisplay();
}

void FormPaymentSelect::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event);
    // 背景与边框统一绘制
    QRect rectClient = this->rect();
    QPainter painter(this);
    painter.setBrush(m_colorBackground);
    painter.setPen(Qt::black);
    painter.drawRect(rectClient.x(), rectClient.y(), rectClient.width() - 1, rectClient.height() - 1);
}

int FormPaymentSelect::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    // 无条件日志，确认方法被调用
    InfoLog("FormPaymentSelect::mtcKeyPressed 被调用");

    if (!mtcKeyEvent) {
        InfoLog("mtcKeyPressed: mtcKeyEvent 为空");
        return 0;
    }

    InfoLog(QString("mtcKeyPressed: 收到按键事件，key=%1, keyName=%2, isNumKey=%3").arg(mtcKeyEvent->key()).arg(mtcKeyEvent->keyName()).arg(mtcKeyEvent->isNumKey()));

    if (mtcKeyEvent->isNumKey()) {
        // 处理数字键 - 修复键值计算
        int keyValue = mtcKeyEvent->func() - Key0; // 得到 0-9
        InfoLog(QString("数字键处理：原始key=%1, Key0=%2, keyValue=%3").arg(mtcKeyEvent->key()).arg(Key0).arg(keyValue));

        if (keyValue >= 1 && keyValue <= 9) {
            ProcessNumberSelection(keyValue);
        } else {
            InfoLog(QString("无效的数字键：%1").arg(keyValue));
        }
        return 1;
    } else if (mtcKeyEvent->func() == KeyEsc) {
        // 处理ESC键
        ProcessEscapeKey();
        return 1;
    } else if (mtcKeyEvent->func() == KeyConfirm) {
        // 处理确认键
        ProcessConfirmKey();
        return 1;
    }
    
    return CBaseOpWidget::mtcKeyPressed(mtcKeyEvent);
}


void FormPaymentSelect::UpdatePaymentDisplay()
{
    ClearOptionLabels();
    LayoutOptionLabels();
}

void FormPaymentSelect::UpdateSelectionHighlight()
{
    // 高亮当前选项
    for (int i = 0; i < m_optionLabels.size(); ++i) {
        QString normal   = "color: rgb(0,0,0); background-color: transparent; padding: 6px 12px; border-radius: 6px;";
        QString selected = "color: rgb(255,255,255); background-color: rgb(0,120,215); padding: 6px 12px; border-radius: 6px;";
        m_optionLabels[i]->setStyleSheet(i == m_selectedIndex ? selected : normal);
    }
}

void FormPaymentSelect::RefreshDisplay()
{
    LayoutOptionLabels();
}

void FormPaymentSelect::ClearOptionLabels()
{
    for (int i = 0; i < m_optionLabels.size(); ++i) {
        if (m_optionLabels[i]) {
            delete m_optionLabels[i];
        }
    }
    m_optionLabels.clear();

    if (m_pOptionsLayout) {
        while (m_pOptionsLayout->count() > 0) {
            QLayoutItem *item = m_pOptionsLayout->takeAt(0);
            if (item->layout()) {
                delete item->layout();
            } else if (item->widget()) {
                item->widget()->deleteLater();
            }
            delete item;
        }
    }
}

void FormPaymentSelect::LayoutOptionLabels()
{
    if (!m_pOptionsLayout) return;

    int optionCount = qMin(m_availablePayTypes.size(), 5);
    QFont optionFont(g_GlobalUI.m_FontName, 16);
    for (int i = 0; i < optionCount; ++i) {
        QLabel *lbl = new QLabel(this);
        lbl->setFont(optionFont);
        lbl->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
        lbl->setText(QString(" %1、 %2").arg(i + 1).arg(GetPayTypeName(m_availablePayTypes[i])));

        QHBoxLayout *h = new QHBoxLayout();
        h->setContentsMargins(0, 0, 0, 0);
        h->addStretch();
        h->addWidget(lbl);
        h->addStretch();
        m_pOptionsLayout->addLayout(h);

        lbl->show();
        m_optionLabels.append(lbl);
    }

    UpdateSelectionHighlight();
}

void FormPaymentSelect::ProcessNumberSelection(int number)
{
    InfoLog(QString("ProcessNumberSelection 被调用，number=%1, 可用支付方式数量=%2").arg(number).arg(m_availablePayTypes.size()));

    if (number >= 1 && number <= m_availablePayTypes.size()) {
        // 仅记录选择并高亮显示，不直接完成
        SelectPaymentType(number - 1); // 内部会更新 m_selectedIndex / m_selectedPayType 并刷新高亮
        InfoLog(QString("高亮选择支付方式：%1 (索引:%2)").arg(GetPayTypeName(m_selectedPayType)).arg(m_selectedIndex));
    } else {
        // 无效选择
        InfoLog(QString("无效的支付方式选择：%1，有效范围：1-%2").arg(number).arg(m_availablePayTypes.size()));
    }
}

void FormPaymentSelect::ProcessEscapeKey()
{
    InfoLog("用户取消支付方式选择");
    CancelSelection();
}

void FormPaymentSelect::ProcessConfirmKey()
{
    if (IsValidSelection(m_selectedIndex)) {
        m_selectedPayType = m_availablePayTypes[m_selectedIndex];
        CompleteSelection();
    }
}

void FormPaymentSelect::SelectPaymentType(int index)
{
    if (index >= 0 && index < m_availablePayTypes.size()) {
        m_selectedIndex = index;
        m_selectedPayType = m_availablePayTypes[index];
        UpdateSelectionHighlight();
    }
}

void FormPaymentSelect::CompleteSelection()
{
    InfoLog(QString("CompleteSelection 被调用，选择的支付方式：%1").arg(GetPayTypeName(m_selectedPayType)));
    m_bSelectionMade = true;
    OnOk(); // 关闭对话框并返回OK
}

void FormPaymentSelect::CancelSelection()
{
    m_bSelectionMade = false;
    OnCancel(); // 关闭对话框并返回Cancel
}

QString FormPaymentSelect::GetPayTypeName(CTransPayType payType)
{
    switch (payType) {
        case TransPT_Cash: return QString::fromUtf8("现金");
        case TransPT_Union: return QString::fromUtf8("银联卡");      // 银联卡代表移动支付（支付宝/微信）
        case TransPT_AliPay: return QString::fromUtf8("银联卡");     // 支付宝归类为银联卡（移动支付）
        case TransPT_WeChat: return QString::fromUtf8("银联卡");     // 微信归类为银联卡（移动支付）
        case TransPT_ETCCard: return QString::fromUtf8("赣通卡");    // ETC显示为赣通卡
        default: return QString::fromUtf8("未知");
    }
}

QString FormPaymentSelect::GetPayTypeDescription(CTransPayType payType)
{
    switch (payType) {
        case TransPT_Cash: return QString::fromUtf8("现金支付");
        case TransPT_Union: return QString::fromUtf8("银联卡移动支付");     // 银联卡代表移动支付
        case TransPT_AliPay: return QString::fromUtf8("银联卡移动支付");    // 支付宝归类为银联卡移动支付
        case TransPT_WeChat: return QString::fromUtf8("银联卡移动支付");    // 微信归类为银联卡移动支付
        case TransPT_ETCCard: return QString::fromUtf8("赣通卡支付");       // ETC描述为赣通卡支付
        default: return QString::fromUtf8("未知支付方式");
    }
}

bool FormPaymentSelect::IsValidSelection(int index)
{
    return index >= 0 && index < m_availablePayTypes.size();
}
