#ifndef REPAYFUNCTIONSWITCHER_H
#define REPAYFUNCTIONSWITCHER_H

#include <QObject>
#include <QFileSystemWatcher>
#include <QMutex>
#include "../config/repayconfig.h"
#include "../../common/lanetype.h"
#include "../ui/formrepaynew.h"
#include "../ui/formauthorization.h"

/**
 * @brief 功能切换控制器
 * 负责控制现有补费功能和新版补费功能之间的切换
 * 提供统一的补费功能入口点
 */
class RepayFunctionSwitcher : public QObject
{
    Q_OBJECT
    
public:
    static RepayFunctionSwitcher* GetInstance();
    
    // 初始化功能切换器
    bool Initialize();
    
    // 获取当前补费功能版本
    bool IsUseNewRepayFunction() const;
    
    // 设置补费功能版本（运行时切换）
    bool SetUseNewRepayFunction(bool useNew);
    
    // 重新加载配置
    void ReloadConfig();
    
    // 处理补费按键事件（统一入口）
    void HandleRepayKeyEvent(const CVehInfo &vehInfo);
    
    // 获取当前功能版本名称
    QString GetCurrentFunctionName() const;
    
    // 验证功能切换的合法性
    bool ValidateSwitch(bool useNewFunction) const;

signals:
    // 配置变更信号
    void RepayFunctionChanged(bool useNewFunction);
    
    // 切换完成信号
    void SwitchCompleted(bool success, const QString &message);
    
    // 切换通知信号
    void SwitchNotification(const QString &message);

private slots:
    // 配置文件监控
    void OnConfigFileChanged();
    
    // 响应配置变更
    void OnConfigChanged(const QString &section, const QString &key, const QVariant &value);
    
    // 延时重新加载配置
    void OnDelayedReload();

private:
    RepayFunctionSwitcher(QObject *parent = 0);
    ~RepayFunctionSwitcher();
    
    // 显示切换通知
    void ShowSwitchNotification(bool useNewFunction);
    
    // 调用现有补费功能
    void CallLegacyRepayFunction(const CVehInfo &vehInfo);
    
    // 调用新版补费功能
    void CallNewRepayFunction(const CVehInfo &vehInfo);
    
private:
    static RepayFunctionSwitcher *m_pInstance;
    static QMutex m_mutex;
    
    bool m_bUseNewFunction;           // 是否使用新版功能
    bool m_bAllowRuntimeSwitch;      // 是否允许运行时切换
    bool m_bSwitchNotificationEnabled; // 是否启用切换通知
    bool m_bInitialized;             // 是否已初始化
    
    QFileSystemWatcher *m_pConfigWatcher;  // 配置文件监控器
    RepayConfig *m_pRepayConfig;           // 配置管理器
    FormRepayNew *m_pRepayForm;            // 当前补费流程控制器（动态创建）
};

#endif // REPAYFUNCTIONSWITCHER_H 