#include "formselectbigveh.h"
#include "globalui.h"
#include "MtcKey/MtcKeyDef.h"
#include "dlgmain.h"
#include <QDateTime>
#include <QApplication>

FormSelectBigVeh::FormSelectBigVeh(QWidget *parent) : CBaseOpWidget(parent)
    , m_pTableWidget(NULL)
    , m_pBtnConfirm(NULL)
    , m_pBtnCancel(NULL)
    , m_pLblTitle(NULL)
    , m_pLblInstruction(NULL)
    , m_nSelectedIndex(-1)
    , m_bConfirmed(false)
{
    setObjectName("FormSelectBigVeh");
    filterChildrenKeyEvent();
}

FormSelectBigVeh::~FormSelectBigVeh()
{
    // Qt会自动清理子控件，但为了确保安全，手动删除
    if (m_pTableWidget) {
        m_pTableWidget->deleteLater();
        m_pTableWidget = NULL;
    }
    if (m_pBtnConfirm) {
        m_pBtnConfirm->deleteLater();
        m_pBtnConfirm = NULL;
    }
    if (m_pBtnCancel) {
        m_pBtnCancel->deleteLater();
        m_pBtnCancel = NULL;
    }
    if (m_pLblTitle) {
        m_pLblTitle->deleteLater();
        m_pLblTitle = NULL;
    }
    if (m_pLblInstruction) {
        m_pLblInstruction->deleteLater();
        m_pLblInstruction = NULL;
    }
}

bool FormSelectBigVeh::ShowSelectDialog(const QList<CBigVehInfo> &bigVehList, CBigVehInfo &selectedInfo)
{
    // 如果列表为空，直接返回false
    if (bigVehList.isEmpty()) {
        return false;
    }
    
    // 保存大件车列表
    m_bigVehList = bigVehList;
    m_nSelectedIndex = 0;  // 默认选中第一项
    m_bConfirmed = false;
    
    // 初始化界面
    InitUI();
    
    // 设置表格数据
    SetTableData(m_bigVehList);
    
    // 显示对话框（模态）
    int result = doModalShow();
    
    // 如果用户确认选择且有有效的选择索引
    if (m_bConfirmed && m_nSelectedIndex >= 0 && m_nSelectedIndex < m_bigVehList.size()) {
        selectedInfo = m_bigVehList[m_nSelectedIndex];
        return true;
    }
    
    return false;
}

void FormSelectBigVeh::InitUI(int iFlag)
{
    CBaseOpWidget::InitUI(iFlag);
    
    // 设置窗口标题
    SetTitle("大件车选择");
    
    // 使用全局UI配置的字体和尺寸，参照原有大件车输入界面
    QFont fontTitle = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TitleFontSize);
    QFont fontText = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TextFontSize);
    QFont fontEdit = QFont(g_GlobalUI.m_FontName, g_GlobalUI.bigtruck_EditFontSize);
    
    // 计算布局尺寸
    int padding = 10;
    int titleHeight = g_GlobalUI.optw_TitleHeight;
    int buttonHeight = 40;
    int instructionHeight = 25;
    int bottomSpace = buttonHeight + instructionHeight + padding * 3;
    
    // 创建标题标签
    if (!m_pLblTitle) {
        m_pLblTitle = new QLabel(this);
    }
    m_pLblTitle->setText("发现多个大件车记录，请选择：");
    m_pLblTitle->setAlignment(Qt::AlignCenter);
    m_pLblTitle->setFont(fontTitle);
    QRect titleRect(padding, titleHeight, rect().width() - 2 * padding, 30);
    m_pLblTitle->setGeometry(titleRect);
    
    // 创建表格控件
    if (!m_pTableWidget) {
        m_pTableWidget = new QTableWidget(this);
        connect(m_pTableWidget, SIGNAL(currentCellChanged(int,int,int,int)), 
                this, SLOT(OnTableCurrentChanged(int,int,int,int)));
    }
    
    // 表格位置：在标题下方，底部留出按钮和说明的空间
    int tableTop = titleRect.bottom() + padding;
    int tableHeight = rect().height() - tableTop - bottomSpace;
    QRect tableRect(padding, tableTop, rect().width() - 2 * padding, tableHeight);
    m_pTableWidget->setGeometry(tableRect);
    
    // 设置表格列
    QStringList headers;
    headers << "序号" << "证书号" << "牵引车牌" << "挂车车牌" << "通行起始时间" << "通行结束时间" << "载货信息";
    m_pTableWidget->setColumnCount(headers.size());
    m_pTableWidget->setHorizontalHeaderLabels(headers);
    
    // 设置表格样式
    SetTableStyle();
    
    // 创建操作说明标签
    if (!m_pLblInstruction) {
        m_pLblInstruction = new QLabel(this);
    }
    m_pLblInstruction->setText("按【↑】【↓】键选择记录，按【确认】键确认，按【ESC】键返回");
    m_pLblInstruction->setAlignment(Qt::AlignCenter);
    m_pLblInstruction->setFont(fontText);
    QRect instructionRect(padding, tableRect.bottom() + padding, rect().width() - 2 * padding, instructionHeight);
    m_pLblInstruction->setGeometry(instructionRect);
    
    // 计算按钮位置：底部居中排列
    int buttonWidth = 100;
    int buttonSpacing = 20;
    int totalButtonWidth = buttonWidth * 2 + buttonSpacing;
    int buttonLeft = (rect().width() - totalButtonWidth) / 2;
    int buttonTop = instructionRect.bottom() + padding;
    
    // 创建确认按钮
    if (!m_pBtnConfirm) {
        m_pBtnConfirm = new QPushButton(this);
        connect(m_pBtnConfirm, SIGNAL(clicked()), this, SLOT(OnConfirm()));
    }
    m_pBtnConfirm->setText("确定");
    m_pBtnConfirm->setFont(fontText);
    m_pBtnConfirm->setGeometry(buttonLeft, buttonTop, buttonWidth, buttonHeight);
    
    // 创建取消按钮
    if (!m_pBtnCancel) {
        m_pBtnCancel = new QPushButton(this);
        connect(m_pBtnCancel, SIGNAL(clicked()), this, SLOT(OnCancel()));
    }
    m_pBtnCancel->setText("取消");
    m_pBtnCancel->setFont(fontText);
    m_pBtnCancel->setGeometry(buttonLeft + buttonWidth + buttonSpacing, buttonTop, buttonWidth, buttonHeight);
}

int FormSelectBigVeh::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    if (!mtcKeyEvent) {
        return 0;
    }
    
    // 数字键 - 直接选择对应序号的记录
    if (mtcKeyEvent->isNumKey()) {
        mtcKeyEvent->setKeyType(KC_Number);
        int index = mtcKeyEvent->ascii() - '1';  // 数字键从1开始，索引从0开始
        if (index >= 0 && index < m_bigVehList.size()) {
            m_nSelectedIndex = index;
            m_pTableWidget->setCurrentCell(m_nSelectedIndex, 0);
        }
        return 1;
    }
    
    // 功能键处理
    if (mtcKeyEvent->isFuncKey()) {
        mtcKeyEvent->setKeyType(KC_Func);
        int nFuncKey = mtcKeyEvent->func();
        
        switch (nFuncKey) {
            case KeyUp:
                // 上箭头键 - 向上移动选择
                if (m_nSelectedIndex > 0) {
                    m_nSelectedIndex--;
                    m_pTableWidget->setCurrentCell(m_nSelectedIndex, 0);
                }
                return 1;
                
            case KeyDown:
                // 下箭头键 - 向下移动选择
                if (m_nSelectedIndex < m_bigVehList.size() - 1) {
                    m_nSelectedIndex++;
                    m_pTableWidget->setCurrentCell(m_nSelectedIndex, 0);
                }
                return 1;
                
            case KeyConfirm:
                // 确定键 - 确认选择
                OnConfirm();
                return 1;
                
            case KeyEsc:
                // 取消键 - 取消选择
                OnCancel();
                return 1;
                
            default:
                break;
        }
    }
    
    return CBaseOpWidget::mtcKeyPressed(mtcKeyEvent);
}

void FormSelectBigVeh::OnConfirm()
{
    if (m_nSelectedIndex >= 0 && m_nSelectedIndex < m_bigVehList.size()) {
        m_bConfirmed = true;
        setModalResult(Rlt_OK);
    }
}

void FormSelectBigVeh::OnCancel()
{
    m_bConfirmed = false;
    setModalResult(Rlt_Cancel);
}

void FormSelectBigVeh::OnTableCurrentChanged(int currentRow, int currentColumn, int previousRow, int previousColumn)
{
    Q_UNUSED(currentColumn);
    Q_UNUSED(previousRow);
    Q_UNUSED(previousColumn);
    
    // 更新选中索引
    m_nSelectedIndex = currentRow;
}

void FormSelectBigVeh::SetTableData(const QList<CBigVehInfo> &bigVehList)
{
    if (!m_pTableWidget) {
        return;
    }
    
    // 设置行数
    m_pTableWidget->setRowCount(bigVehList.size());
    
    // 填充数据
    for (int i = 0; i < bigVehList.size(); i++) {
        const CBigVehInfo &info = bigVehList[i];
        
        // 序号
        QTableWidgetItem *itemIndex = new QTableWidgetItem(QString::number(i + 1));
        itemIndex->setTextAlignment(Qt::AlignCenter);
        m_pTableWidget->setItem(i, 0, itemIndex);
        
        // 证书号
        QTableWidgetItem *itemCerNo = new QTableWidgetItem(info.cerNo);
        itemCerNo->setTextAlignment(Qt::AlignCenter);
        m_pTableWidget->setItem(i, 1, itemCerNo);
        
        // 牵引车牌
        QTableWidgetItem *itemTractorPlate = new QTableWidgetItem(info.tractor_vehicle_vlp);
        itemTractorPlate->setTextAlignment(Qt::AlignCenter);
        m_pTableWidget->setItem(i, 2, itemTractorPlate);
        
        // 挂车车牌
        QTableWidgetItem *itemTrailerPlate = new QTableWidgetItem(info.trailer_vehicle_vlp);
        itemTrailerPlate->setTextAlignment(Qt::AlignCenter);
        m_pTableWidget->setItem(i, 3, itemTrailerPlate);
        
        // 通行起始时间
        QTableWidgetItem *itemStartTime = new QTableWidgetItem(FormatDateTime(info.startPassDate));
        itemStartTime->setTextAlignment(Qt::AlignCenter);
        m_pTableWidget->setItem(i, 4, itemStartTime);
        
        // 通行结束时间
        QTableWidgetItem *itemEndTime = new QTableWidgetItem(FormatDateTime(info.endPassDate));
        itemEndTime->setTextAlignment(Qt::AlignCenter);
        m_pTableWidget->setItem(i, 5, itemEndTime);
        
        // 载货信息
        QTableWidgetItem *itemGoods = new QTableWidgetItem(info.goodsInfo);
        itemGoods->setTextAlignment(Qt::AlignLeft | Qt::AlignVCenter);
        m_pTableWidget->setItem(i, 6, itemGoods);
    }
    
    // 选中第一行
    if (bigVehList.size() > 0) {
        m_pTableWidget->setCurrentCell(0, 0);
        m_nSelectedIndex = 0;
    }
    
    // 调整列宽
    m_pTableWidget->resizeColumnsToContents();
    
    // 确保序号列不会太宽
    m_pTableWidget->setColumnWidth(0, 60);
}

CBigVehInfo FormSelectBigVeh::GetSelectedBigVehInfo()
{
    if (m_nSelectedIndex >= 0 && m_nSelectedIndex < m_bigVehList.size()) {
        return m_bigVehList[m_nSelectedIndex];
    }
    
    return CBigVehInfo();  // 返回空的大件车信息
}

void FormSelectBigVeh::SetTableStyle()
{
    if (!m_pTableWidget) {
        return;
    }
    
    // 使用全局UI配置的字体
    QFont headerFont = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TextFontSize, QFont::Bold);
    QFont tableFont = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TextFontSize);
    
    // 设置表格样式
    m_pTableWidget->setAlternatingRowColors(true);  // 交替行颜色
    m_pTableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);  // 选择整行
    m_pTableWidget->setSelectionMode(QAbstractItemView::SingleSelection);  // 单选模式
    m_pTableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);    // 禁止编辑
    
    // 设置表头样式
    QHeaderView *header = m_pTableWidget->horizontalHeader();
    if (header) {
        header->setResizeMode(QHeaderView::Interactive);  // 允许调整列宽
        header->setDefaultSectionSize(120);  // 默认列宽
        header->setFont(headerFont);
    }
    
    // 设置垂直表头
    QHeaderView *vHeader = m_pTableWidget->verticalHeader();
    if (vHeader) {
        vHeader->setVisible(false);  // 隐藏行号
    }
    
    // 设置表格字体
    m_pTableWidget->setFont(tableFont);
    
    // 设置行高
    m_pTableWidget->verticalHeader()->setDefaultSectionSize(30);
    
    // 设置表格边框
    m_pTableWidget->setShowGrid(true);
    m_pTableWidget->setGridStyle(Qt::SolidLine);
    
    // 设置表格背景色，使用与系统一致的颜色方案
    m_pTableWidget->setStyleSheet(
        "QTableWidget {"
        "    background-color: white;"
        "    border: 2px solid gray;"
        "    selection-background-color: lightblue;"
        "}"
        "QTableWidget::item {"
        "    border-bottom: 1px solid lightgray;"
        "    padding: 5px;"
        "}"
        "QTableWidget::item:selected {"
        "    background-color: #4A90E2;"
        "    color: white;"
        "}"
        "QHeaderView::section {"
        "    background-color: #E0E0E0;"
        "    border: 1px solid gray;"
        "    padding: 8px;"
        "}"
    );
}

QString FormSelectBigVeh::FormatDateTime(quint32 timestamp)
{
    if (timestamp == 0) {
        return "无限期";
    }
    
    // 将时间戳转换为QDateTime（假设timestamp是Unix时间戳）
    QDateTime dateTime = QDateTime::fromTime_t(timestamp);
    
    // 格式化为中国时间格式
    return dateTime.toString("yyyy-MM-dd hh:mm:ss");
} 
