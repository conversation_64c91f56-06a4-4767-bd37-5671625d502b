#ifndef FORMREPAYTYPESELECT_H
#define FORMREPAYTYPESELECT_H

/**
 * 单元说明：
 *   本单元定义了新版补费功能中的“补费类型选择”页面类 `FormRepayTypeSelect`。
 *   该页面用于替代原先通过 `CFuncMenu::DoNewRepayMenu()` 的菜单选择方式，
 *   以统一到本项目的操作页面体系中（继承自 `CBaseOpWidget`）。
 *
 * 开发环境与规范：
 *   - 平台：Qt 4.8.5（请勿使用 nullptr，统一使用 0）
 *   - 时区：上海/北京时区（本页面不涉及时间换算逻辑）
 *   - 代码风格：提供详细中文说明，包含类说明、函数说明及关键代码说明
 */

#include <QWidget>
#include <QDateTime>
#include <QLabel>
#include <QFont>
#include <QColor>

#include "../../baseopwidget.h"
#include "../common/repaytypes.h"

class QPushButton;

/**
 * 类说明：
 *   `FormRepayTypeSelect` 为补费类型选择页面。页面提供“当趟补费”和“省内名单补费”两种选择，
 *   支持按钮点击与功能键（KeyCar1/KeyCar2）快捷选择，最终将用户选择的补费类型通过
 *   `ShowSelect` 接口返回给调用方。
 */
class FormRepayTypeSelect : public CBaseOpWidget
{
    Q_OBJECT

public:
    /**
     * 构造函数
     * @param parent 父窗口指针（缺省为 0）
     */
    explicit FormRepayTypeSelect(QWidget *parent = 0);

    /** 析构函数 */
    virtual ~FormRepayTypeSelect();

    /**
     * 初始化界面
     * @param iFlag 预留标志位（与现有体系保持一致）
     */
    virtual void InitUI(int iFlag = 0);

    /**
     * 模态显示选择页面，并返回用户选择
     * @param selectedType 输出参数，返回用户选择的补费类型
     * @return true 表示用户确认选择；false 表示用户取消
     */
    bool ShowSelect(RepayType &selectedType);

protected:
    /** 模态显示后的回调，用于聚焦与提示 */
    virtual void OnModalShowed();

    /**
     * 处理车道功能键（与项目既有按键体系一致）
     * - KeyEsc：取消
     * - Key1：选择当趟补费
     * - Key2：选择省内名单补费
     * - KeyConfirm：确认当前选择
     */
    virtual int mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent);

private slots:
    /** 点击“当趟补费” */
    void OnSelectCurrent();
    /** 点击“省内名单补费” */
    void OnSelectProvince();
    /** 点击“取消” */
    void OnCancelClicked();

private:
    /** 构建界面控件与布局 */
    void BuildUI();

    /** 更新提示信息（底部帮助文字） */
    void UpdateHint();

    /** 根据索引更新选中态 */
    void SetCurrentSelection(int index);

private:
    // 界面元素
    QLabel *m_lblTitle;      // 标题：补费方式
    QLabel *m_lblOpt1;       // 1、当趟补费
    QLabel *m_lblOpt2;       // 2、省内名单补费
    QLabel *m_lblHelp;       // 底部提示

    // 样式配置
    QFont  m_fontTitle;
    QFont  m_fontOption;
    QFont  m_fontHelp;
    QColor m_colorBackground;
    QColor m_colorNormal;
    QColor m_colorSelected;

    RepayType    m_selectedType; // 记录当前选择结果
    bool         m_hasSelection; // 是否已选择
    int          m_currentIndex; // 当前光标：0=当趟；1=省内
};

#endif // FORMREPAYTYPESELECT_H


