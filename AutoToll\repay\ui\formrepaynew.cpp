#include "formrepaynew.h"
#include "formauthorization.h"
#include "../../globalui.h"
#include "../../dlgmain.h"
#include "../../log4qt/ilogmsg.h"
#include "JlCompress.h"
#include <QApplication>
#include <QKeyEvent>
#include <QMessageBox>
#include <QRegExp>
#include <QTextCodec>
#include <QFile>
#include "forminterceptselect.h"
#include "../config/repayconfig.h"
#include "FormDebtDetailSimple.h"
#include "../../common/paramfilemgr.h"
#include "../../common/paramfile.h"

FormRepayNew::FormRepayNew(QObject *parent)
    : QObject(parent)
    , m_repayType(RepayType_None)
    , m_vehPlateColor(1)
    , m_vehType(VC_Car1)
    , m_amount(0)
    , m_currentPayType(TransPT_Cash)
    , m_bProcessing(false)
    , m_bInputEnabled(true)
    , m_bPaymentEnabled(false)
    , m_paymentInProgress(false)
    , m_pProDebPayment(0)
    , m_pAuthManager(0)
    , m_pRepayConfig(0)
    , m_pQueryTimer(0)
    , m_bConnectionsInited(false)
    , m_bInitialized(false)
{
    // 初始化定时器
    m_pQueryTimer = new QTimer(this);
    
    // 初始化界面配置
    InitUIConfig();
    
    setObjectName(QString("FormRepayNew"));
}

FormRepayNew::~FormRepayNew()
{
    InfoLog("销毁新版补费界面");

    // 无状态设计：不需要清理RepayManager状态
    // 每次补费都是独立的操作，界面关闭不影响后续补费

    // 车牌输入界面已改为局部变量，无需清理

    // 停止所有定时器
    if (m_pQueryTimer) {
        m_pQueryTimer->stop();
    }

    // 断开业务组件信号连接
    if (m_pProDebPayment) {
        disconnect(m_pProDebPayment, 0, this, 0);
    }
    if (m_pAuthManager) {
        disconnect(m_pAuthManager, 0, this, 0);
    }

    DebugLog("新版补费界面资源清理完成");
}

void FormRepayNew::InitUIConfig()
{
    // FormRepayNew已转为纯流程控制器，不再需要界面配置
    // 保留此方法为空实现以兼容现有调用
}

void FormRepayNew::InitUI(int iFlag)
{

    InfoLog("初始化补费界面");
    // 初始化控件 (已移除，现在由外部对话框处理)
    // InitControls();
    // 设置布局 (已移除，现在由外部对话框处理)
    // InitLayout();
    // 连接信号
    InitConnections();
    // 重置界面状态 (已移除，现在由外部对话框处理)
    // ResetUI();

    DebugLog("补费界面初始化完成");
}

void FormRepayNew::InitControls()
{
    // FormRepayNew已转为纯流程控制器，不再管理界面控件
    // 保留此方法为空实现以兼容现有调用
}

void FormRepayNew::SetupControlProperties()
{
    // FormRepayNew已转为纯流程控制器，不再设置界面控件属性
    // 保留此方法为空实现以兼容现有调用
}

void FormRepayNew::SetupButtonStyles()
{
    // FormRepayNew已转为纯流程控制器，不再设置按钮样式
    // 保留此方法为空实现以兼容现有调用
}

void FormRepayNew::InitLayout()
{
    // FormRepayNew已转为纯流程控制器，不再管理界面布局
    // 保留此方法为空实现以兼容现有调用
}







void FormRepayNew::InitConnections()
{
    // 初始化补费组件
    if (!InitializeRepayComponents()) {
        ErrorLog("初始化补费组件失败");
        return;
    }
    
    // 业务组件信号连接
    if (!m_bConnectionsInited) {
        if (m_pProDebPayment) {
            connect(m_pProDebPayment, SIGNAL(QueryDebtInfoFinished(bool,RepayDebtQueryResult)), 
                    this, SLOT(OnDebtQueryFinished(bool,RepayDebtQueryResult)));
            connect(m_pProDebPayment, SIGNAL(NotifyDebtCompleteFinished(bool,QString)), 
                    this, SLOT(OnNotifyCompleteFinished(bool,QString)));
            connect(m_pProDebPayment, SIGNAL(ConfirmCurrentDebtFinished(bool,CurrentDebtConfirmResult)), 
                    this, SLOT(OnCurrentDebtConfirmed(bool,CurrentDebtConfirmResult)));
        }
        if (m_pAuthManager) {
            connect(m_pAuthManager, SIGNAL(AuthorizationCompleted(bool)), 
                    this, SLOT(OnAuthorizationCompleted(bool)));
        }
        m_bConnectionsInited = true;
    }
    
    // 界面控件信号连接（简化后只保留存在的控件）
//    if (m_pEditAmount) {
//        connect(m_pEditAmount, SIGNAL(textChanged(QString)), this, SLOT(OnAmountChanged()));
//    }
    
    // 功能按钮已移除
    
    // 定时器连接
    connect(m_pQueryTimer, SIGNAL(timeout()), this, SLOT(OnQueryTimeout()));
}

void FormRepayNew::SetRepayType(RepayType type)
{
    m_repayType = type;
    UpdateUI();

    QString typeName = GetRepayTypeName(type);
    InfoLog(QString("设置补费类型：%1 (值:%2)").arg(typeName).arg((int)type));
}

void FormRepayNew::SetVehicleInfo(const QString &vehPlate, int vehPlateColor, int vehType)
{
    m_vehPlate = vehPlate;
    m_vehPlateColor = vehPlateColor;
    m_vehType = (CVehClass)vehType;

    // 更新界面显示
    SetVehPlateColor(vehPlateColor);

    InfoLog(QString("设置车辆信息 - 车牌:%1, 颜色:%2, 车型:%3")
            .arg(vehPlate).arg(vehPlateColor).arg(vehType));
}

void FormRepayNew::SetInterceptType(InterceptType interceptType)
{
    m_currentInterceptType = interceptType;
    
    InfoLog(QString("设置拦截方式 - 类型:%1")
            .arg(interceptType == Intercept_Entry ? "入口拦截" : "出口拦截"));
}

void FormRepayNew::SetVehPlateColor(int color)
{
    m_vehPlateColor = color;

    DebugLog(QString("选择车牌颜色：%1").arg(GetVehPlateColorName(color)));
}

bool FormRepayNew::StartRepayProcess(QString &outMessage)
{
    if (!m_bInitialized) {
        ShowErrorMessage("补费组件未初始化");
        outMessage = QString::fromUtf8("补费组件未初始化");
        return false;
    }
    
    // 验证补费条件
    if (!ValidateRepayConditions(m_repayType, m_vehPlate, m_vehPlateColor)) {
        ErrorLog(QString::fromUtf8("补费条件校验失败"));
//        return false;
        // 1. 车型输入界面
        if (!ShowVehTypeInputDialog()) {
            InfoLog("用户取消车型选择");
            return false;
        }

        // 2. 车牌输入界面
        if (!ShowPlateInputDialog()) {
            InfoLog("用户取消车牌输入");
            return false;
        }
    }
    
    InfoLog(QString("开始补费流程 - 类型:%1, 车牌:%2, 车型:%3")
            .arg(GetRepayTypeName(m_repayType))
            .arg(m_vehPlate)
            .arg(m_vehType));
    
    // 启动流程
    m_bProcessing = true;
    m_startTime = QDateTime::currentDateTime();

    
    // 发送流程开始信号
    OnRepayStarted(m_repayType);
    
    // 根据补费类型开始相应的流程
    bool result = false;
    if (m_repayType == RepayType_Current) {
        result = ProcessCurrentRepayFlow();
    } else if (m_repayType == RepayType_Province) {
        result = ProcessProvinceRepayFlow();
    } else {
        ShowErrorMessage("不支持的补费类型");
        outMessage = QString::fromUtf8("不支持的补费类型");
        return false;
    }
    
    if (!result) {
        ShowErrorMessage("补费失败");
        outMessage = QString::fromUtf8("补费失败");
        m_bProcessing = false;
    } else {
        outMessage = QString::fromUtf8("补费完成");
    }

    return result;
}









// 实现车牌输入对话框
bool FormRepayNew::ShowPlateInputDialog()
{
    InfoLog("准备显示车牌输入对话框");

    // 先隐藏当前FormRepayNew页面
    InfoLog("隐藏FormRepayNew界面");
    // 业务逻辑类无需隐藏界面

    // 按照CLaneState_VehInput::InputPlate()的方式创建FormInputPlate
    // 业务逻辑类不再直接创建对话框
    // 应该通过信号通知外部UI层创建对话框
    // TODO: 实现通过信号机制获取车牌输入
    FormInputPlate dlgInput(nullptr);

    InfoLog(QString("创建FormInputPlate对象，初始车牌:%1, 颜色:%2").arg(m_vehPlate).arg(m_vehPlateColor));

    // 调用EditPlate方法显示车牌输入对话框
    bool result = dlgInput.EditPlate(m_vehPlate, static_cast<VP_COLOR>(m_vehPlateColor));

    InfoLog(QString("车牌输入对话框返回结果：%1").arg(result ? "成功" : "取消/失败"));

    if (result) {
        // 获取输入结果
        QString plate;
        VP_COLOR color;
        dlgInput.GetInputResult(plate, color);

        // 保存结果
        m_vehPlate = plate;
        m_vehPlateColor = static_cast<int>(color);

        InfoLog(QString("车牌输入完成 - 车牌:%1, 颜色:%2").arg(plate).arg(static_cast<int>(color)));
    } else {
        InfoLog("车牌输入被取消或失败");
    }

    // 不在这里重新显示界面，让调用方处理
    InfoLog("车牌输入对话框处理完成，返回结果");
    return result;
}

bool FormRepayNew::ShowAmountInputDialog()
{
    InfoLog("准备显示金额输入对话框");

    // 先隐藏当前界面
    // 业务逻辑类无需隐藏界面

    // 创建金额输入对话框
    FormInputAmount dlgAmount(nullptr);

    InfoLog(QString("显示金额输入对话框 - 车牌:%1, 当前金额:%2分").arg(m_vehPlate).arg(m_amount));

    // 显示金额输入对话框
    bool result = dlgAmount.InputAmount(m_amount, m_vehPlate, m_vehPlateColor, m_vehType);

    InfoLog(QString("金额输入对话框返回结果：%1").arg(result ? "成功" : "取消/失败"));

    if (result) {
        // 获取输入的金额
        m_amount = dlgAmount.GetInputAmount();
        InfoLog(QString("金额输入完成 - 金额:%1分").arg(m_amount));
    } else {
        InfoLog("金额输入被取消或失败");
    }

    return result;
}



void FormRepayNew::OnCancel()
{
    InfoLog("补费界面取消");

    // 无状态设计：不需要清理RepayManager状态
    // 每次补费都是独立的操作，界面关闭不影响后续补费
}

// 业务逻辑实现将在下一部分继续...

void FormRepayNew::UpdateUI()
{
    // FormRepayNew已转为纯流程控制器，不再需要更新界面显示
    // 保留此方法为空实现以兼容现有调用
}

void FormRepayNew::ResetUI()
{
    // 重置所有输入
    ClearInputs();
    
    // 重置状态

    m_bProcessing = false;
    m_bInputEnabled = true;
    m_bPaymentEnabled = false;
    
    // 停止定时器
    if (m_pQueryTimer) m_pQueryTimer->stop();
    
    DebugLog("补费流程状态已重置");
}

void FormRepayNew::ClearInputs()
{
    // 无本地金额编辑控件

    m_vehPlate.clear();
    m_vehPlateColor = 1;
    m_amount = 0;

    // 重置车牌颜色选择
    SetVehPlateColor(1);
}


void FormRepayNew::ShowErrorMessage(const QString &message)
{
    // 业务逻辑类不再直接显示界面消息
    ErrorLog(QString("补费流程错误：%1").arg(message));
}

void FormRepayNew::ShowSuccessMessage(const QString &message)
{
    // 业务逻辑类不再直接显示界面消息
    InfoLog(QString("补费流程成功：%1").arg(message));
}

void FormRepayNew::ShowWarningMessage(const QString &message)
{
    // 业务逻辑类不再直接显示界面消息
    WarnLog(QString("补费流程警告：%1").arg(message));
}

// RepayManager信号响应槽函数实现
void FormRepayNew::OnRepayStarted(RepayType type)
{
    InfoLog(QString("补费流程开始 - 类型:%1").arg(static_cast<int>(type)));
    
    m_repayType = type;

    
    // 更新界面状态

    ShowStageMessage("补费流程已开始");
}




void FormRepayNew::OnRepayCompleted(bool success, const QString &message)
{
    InfoLog(QString("补费流程完成 - 成功:%1, 消息:%2").arg(success).arg(message));

    if (success) {
        // 成功后直接关闭主界面
        // 业务逻辑类不再处理UI关闭
        emit repayCompleted(true, "补费成功");
    } else {
        ShowErrorMessage(message.isEmpty() ? "补费失败" : message);
        
        // 重置界面状态，允许重新操作
        SetUIEnabled(true);
        EnableInput(true);
    
    
    }
}

void FormRepayNew::OnPaymentProcessing()
{
    InfoLog("开始处理支付");
    
    ShowStageMessage("正在处理支付，请稍候...");
    SetUIEnabled(false);
    
    // 显示支付进度
    m_paymentInProgress = true;
    UpdateProgressDisplay();
}

void FormRepayNew::OnPaymentCompleted(bool success, const QString &message)
{
    InfoLog(QString("支付处理完成 - 成功:%1, 消息:%2").arg(success).arg(message));
    
    m_paymentInProgress = false;
    
    if (success) {
        ShowSuccessMessage(message.isEmpty() ? "支付成功" : message);
        
        // 支付成功后，设置为完成阶段

    
        
        // 生成补费流水记录
        if (GenerateRepayRecord(m_currentPayType)) {
            InfoLog("补费流水生成成功");
        } else {
            WarnLog("补费流水生成失败，但支付已成功");
        }
        
        // 支付成功后，根据补费类型进行不同处理
        if (m_repayType == RepayType_Province) {
            // 省内名单补费：后台异步通知省中心，立即显示完成确认界面
            NotifyProvinceCenterInternal();
            //ShowRepayCompleteDialog();
            //ShowRepaySuccessDialog(m_currentPayType);
        } else {
            // 其他补费类型：先显示成功确认界面
            //if (ShowRepaySuccessDialog(m_currentPayType)) {
                // 关闭补费界面
            //    emit repayCompleted(true, "补费成功");
            //} else {
                // 用户取消成功界面，也关闭补费界面
                emit repayCompleted(true, "补费成功");
            //}
        }
    } else {
        ShowErrorMessage(message.isEmpty() ? "支付失败" : message);
        
        // 支付失败，返回金额确认阶段

        SetUIEnabled(true);
        EnablePaymentButtons(true);
    
    }
}

void FormRepayNew::OnDebtQueryCompleted(bool success, const RepayDebtQueryResult &result)
{
    InfoLog(QString("欠费查询完成 - 成功:%1").arg(success));
    
    // 停止查询超时定时器
    if (m_pQueryTimer) {
        m_pQueryTimer->stop();
    }
    
    if (success) {
        m_debtResult = result;
        
                // 根据查询结果更新界面
        if (!result.listno.isEmpty() && result.totalAmount > 0) {  // 查询成功
            InfoLog(QString("查询成功，共%1条欠费记录，总金额:%2元")
                              .arg(result.debtItems.size())
                              .arg(QString::number(result.totalAmount / 100.0, 'f', 2)));
            
            // 显示债务详情界面让用户选择具体补费项目
            if (ShowDebtDetailDialog(result)) {
                // 省内名单补费：债务详情确认后直接进入支付方式选择流程
                InfoLog("债务详情确认完成，直接进入支付方式选择");
                StartPaymentProcess();
            } else {
                // 用户取消了债务详情选择，返回车辆输入阶段
                InfoLog("用户取消债务详情选择");
            
                SetUIEnabled(true);
                EnableInput(true);
            
            }
            
        } else {  // 不在名单中或无欠费
            ShowWarningMessage("该车辆不在省内追收名单中或无欠费记录");
            
            // 返回车辆输入阶段
        
            SetUIEnabled(true);
            EnableInput(true);
        
        }
    } else {
        ShowErrorMessage("网络查询失败，请检查网络连接");
        
        // 返回车辆输入阶段
    
        SetUIEnabled(true);
        EnableInput(true);
    
    }
}

void FormRepayNew::OnRepayError(RepayErrorCode errorCode, const QString &message)
{
    ErrorLog(QString("补费流程错误 - 代码:%1, 消息:%2").arg(static_cast<int>(errorCode)).arg(message));

    // 停止所有定时器
    if (m_pQueryTimer) {
        m_pQueryTimer->stop();
    }
    
    QString errorText;
    switch (errorCode) {
        case RepayError_NetworkFailed:
            errorText = "网络连接失败";
            break;
        case RepayError_AuthFailed:
            errorText = "授权验证失败";
            break;
        case RepayError_AmountExceeded:
            errorText = "补费金额超过限制";
            break;
        case RepayError_VehicleNotFound:
            errorText = "车辆信息未找到";
            break;
        case RepayError_PaymentFailed:
            errorText = "支付处理失败";
            break;
        case RepayError_SystemError:
            errorText = "系统内部错误";
            break;
        default:
            errorText = "未知错误";
            break;
    }
    
    if (!message.isEmpty()) {
        errorText += QString("：%1").arg(message);
    }
    
    ShowErrorMessage(errorText);
    
    // 重置界面状态
    SetUIEnabled(true);
    
    // 根据错误类型决定返回的阶段
    if (errorCode == RepayError_AuthFailed) {
        // 授权失败，需要重新开始
    
        EnableInput(true);
    } else if (errorCode == RepayError_VehicleNotFound || errorCode == RepayError_NetworkFailed) {
        // 车辆或网络问题，返回输入阶段
    
        EnableInput(true);
    } else if (errorCode == RepayError_AmountExceeded) {
        // 金额问题，返回金额确认阶段

        EnablePaymentButtons(true);
    } else {
        // 其他错误，返回输入阶段
    
        EnableInput(true);
    }
    

}

QString FormRepayNew::GetVehPlateColorName(int color)
{
    switch (color) {
        case 1: return "蓝牌";
        case 2: return "黄牌";
        case 3: return "白牌";
        case 4: return "黑牌";
        case 5: return "绿牌";
        default: return "未知";
    }
}












// 界面控制实现
void FormRepayNew::EnableInput(bool enabled)
{
    // FormRepayNew作为流程控制器，只维护状态标志
    m_bInputEnabled = enabled;
}

void FormRepayNew::EnablePaymentButtons(bool enabled)
{
    // FormRepayNew作为流程控制器，只维护状态标志
    m_bPaymentEnabled = enabled;
}

void FormRepayNew::SetUIEnabled(bool enabled)
{
    EnableInput(enabled);
    EnablePaymentButtons(enabled);
}





void FormRepayNew::ShowStageMessage(const QString &message)
{
    // 使用项目标准的信息显示方式 - 阶段提示信息
    // 业务逻辑类不再直接操作界面
    InfoLog(QString("显示消息: %1").arg(message));
}

void FormRepayNew::UpdateProgressDisplay()
{
    // FormRepayNew作为流程控制器，不再显示进度指示器
    // 保留此方法为空实现以兼容现有调用
}

// 输入验证实现

bool FormRepayNew::ValidateAmount()
{
    // 金额必须为正
    if (m_amount <= 0) {
        // 业务逻辑类不再直接显示消息
        ErrorLog("补费金额无效，请输入大于0的金额");
        // 取消补费流程
        // 延迟发送取消信号
        QTimer::singleShot(300, this, SLOT(OnDelayedCancel()));
        return false;
    }

    // 从参数表/配置中获取对应车型的最大可补金额（单位：分）
    int maxAllowedAmount = 0;
    RepayConfig *pCfg = RepayConfig::GetInstance();
    if (pCfg) {
        // 确保配置已初始化
        pCfg->Initialize();
        int typeMax = pCfg->GetMaxFee(static_cast<int>(m_vehType));
        int globalMax = pCfg->GetGlobalMaxFee();
        // 取车型限额与全局限额中的较小值
        if (typeMax > 0 && globalMax > 0)
            maxAllowedAmount = qMax(typeMax, globalMax);
        else if (typeMax > 0)
            maxAllowedAmount = typeMax;
        else if (globalMax > 0)
            maxAllowedAmount = globalMax;
    }

    // 兜底：若未能获取到有效上限，使用默认规则所对应的大致上限（500元）
    if (maxAllowedAmount <= 0) {
        maxAllowedAmount = 50000;  // 单位：分
    }

    if (m_amount > maxAllowedAmount) {
        // 业务逻辑类不再直接显示消息
        QString msg = QString::fromUtf8("补费金额超限，最大允许 %1 元")
                           .arg(QString::number(maxAllowedAmount / 100.0, 'f', 2));
        ErrorLog(msg);
        // 取消补费流程
        // 延迟发送取消信号
        QTimer::singleShot(300, this, SLOT(OnDelayedCancel()));
        return false;
    }

    return true;
}

bool FormRepayNew::ValidateInputs()
{
    if (!ValidateVehPlate()) {
        ShowErrorMessage("请输入正确的车牌号");
        return false;
    }

    if (m_repayType == RepayType_Current && !ValidateAmount()) {
        ShowErrorMessage("请输入有效的补费金额");
        return false;
    }

    if (m_repayType == RepayType_Province && (m_debtResult.listno.isEmpty() || m_debtResult.totalAmount <= 0)) {
        ShowErrorMessage("请先查询欠费信息");
        return false;
    }
    
    return true;
}

// 界面更新实现
void FormRepayNew::UpdateDebtInfo(const RepayDebtQueryResult &result)
{
    QString debtInfo = QString("查询到欠费记录：\n车牌：%1\n欠费金额：%2元\n工单数：%3个")
                      .arg(result.vehiclePlate.isEmpty() ? m_vehPlate : result.vehiclePlate)
                      .arg(QString::number(result.totalAmount / 100.0, 'f', 2))
                      .arg(result.debtItems.size());
    
    Q_UNUSED(debtInfo);
    
    // 设置金额
    m_amount = result.totalAmount;
}

// 注意：车牌颜色和支付方式现在通过键盘输入和单独页面处理，不再需要按钮槽函数

// 延时操作slot实现
void FormRepayNew::OnDelayedOk()
{
    // 业务逻辑类不再处理UI关闭
    emit repayCompleted(true, "补费成功");
}

void FormRepayNew::OnDelayedCancel()
{
    InfoLog("延时关闭补费界面 - 取消");
    emit repayCompleted(false, "补费取消");
}

void FormRepayNew::OnDelayedSuccess()
{
    InfoLog("延时关闭补费界面 - 成功完成");
    emit repayCompleted(true, "补费成功");
}

// 业务处理方法实现
void FormRepayNew::PerformAuthorization()
{
    InfoLog("开始进行班长授权验证");
    
    // 创建授权验证界面
    FormAuthorization authForm;
    
    QString operatorId, operatorName;
    bool authResult = authForm.DoAuthorization(operatorId, operatorName);
    
    if (authResult) {
        InfoLog(QString("授权验证成功 - 操作员:%1(%2)").arg(operatorName).arg(operatorId));
        
        // 授权成功，进入下一阶段
        // 根据补费类型决定下一阶段
        if (m_repayType == RepayType_Current) {
            // 当趟补费：进入车辆输入阶段
        
        } else if (m_repayType == RepayType_Province) {
            // 省内名单补费：进入车辆输入阶段
        
        }
        
        ShowSuccessMessage("授权验证成功");
    } else {
        ErrorLog("授权验证失败或用户取消");
        
        // 授权失败，结束补费流程
        // 取消补费流程
        
        ShowErrorMessage("授权验证失败，补费流程已取消");
        
        // 延迟关闭界面
        // 延迟发送取消信号
        QTimer::singleShot(2000, this, SLOT(OnDelayedCancel()));
    }
}

void FormRepayNew::StartVehicleInput()
{
    InfoLog(QString("开始车辆输入流程，当前补费类型：%1").arg((int)m_repayType));

    // 根据补费类型决定流程
    if (m_repayType == RepayType_Current) {
        InfoLog("当趟补费模式");

        InfoLog("正在启动车型输入界面...");

        // 步骤1：车型输入（调整顺序：先车型后车牌）
        InfoLog("步骤1：车型输入");
        if (!ShowVehTypeInputDialog()) {
            InfoLog("车型输入取消，结束流程");
            // 业务逻辑类不再处理UI关闭
            emit repayCompleted(false, "用户取消操作");
            return;
        }
        InfoLog("车型输入成功，继续车牌输入");

        // 步骤2：车牌输入
        InfoLog("步骤2：车牌输入");
        if (!ShowPlateInputDialog()) {
            InfoLog("车牌输入取消，结束流程");
            // 业务逻辑类不再处理UI关闭
            emit repayCompleted(false, "用户取消操作");
            return;
        }
        InfoLog("车牌输入成功，继续金额输入");

        // 步骤3：金额输入
        InfoLog("步骤3：金额输入");
        if (!ShowAmountInputDialog()) {
            InfoLog("金额输入取消，结束流程");
            // 业务逻辑类不再处理UI关闭
            emit repayCompleted(false, "用户取消操作");
            return;
        }

        InfoLog("金额输入成功，开始确认车辆是否可以当趟补费");

        // 步骤4：确认车辆是否可以当趟补费
        InfoLog("步骤4：车辆当趟补费确认");
        if (!ConfirmVehicleForCurrentRepay()) {
            InfoLog("车辆当趟补费确认失败，结束流程");
            emit repayCompleted(false, "车辆确认失败，无法进行当趟补费");
            return;
        }

        InfoLog("车辆当趟补费确认成功，进入支付流程");

        // 步骤5：支付处理
        InfoLog("步骤5：支付处理");
        ProcessCurrentRepay();

    } else {
        InfoLog("省内名单补费模式");
        
        // 省内名单补费应该通过专用流程启动，这里提示错误
        ErrorLog("省内名单补费不应通过StartVehicleInput启动，请使用StartProvinceRepayFlow");
        ShowErrorMessage("省内名单补费流程配置错误");
        OnCancel();
    }
}

void FormRepayNew::StartProvinceRepayFlow(const QString &vehPlate, int vehPlateColor, const RepayDebtQueryResult &result)
{
    InfoLog(QString("开始省内名单补费专用流程 - 车牌:%1, 总金额:%2分").arg(vehPlate).arg(result.totalAmount));
    
    // 设置补费类型
    m_repayType = RepayType_Province;
    
    // 显示补费信息界面
    if (!ShowProvinceRepayInfoDialog(vehPlate, vehPlateColor, result)) {
        InfoLog("用户取消省内名单补费信息界面，结束流程");
        OnCancel();
        return;
    }
    
    InfoLog("省内名单补费信息确认成功，进入支付流程");
    
    // 进入支付流程（与当趟补费一致）
    ProcessProvinceRepay();
}

void FormRepayNew::ProcessCurrentRepay()
{
    // 当趟补费：直接进入支付流程
    StartPaymentProcess();
}

void FormRepayNew::ProcessProvinceRepay()
{
    // 省内名单补费：查询完成后进入支付流程
    StartPaymentProcess();
}

void FormRepayNew::HandleRepayCompletion(bool success, const QString &message)
{
    if (success) {
        ShowSuccessMessage(message.isEmpty() ? "补费完成" : message);

        // 补费成功，关闭界面
        // 延迟发送成功信号
        QTimer::singleShot(2000, this, SLOT(OnDelayedSuccess()));
    } else {
        ShowErrorMessage(message.isEmpty() ? "补费失败" : message);

        // 重置界面状态，允许重新操作
        SetUIEnabled(true);
        EnableInput(true);
    
    
    }
}

void FormRepayNew::HandleRepayError(RepayErrorCode errorCode, const QString &message)
{
    QString errorMsg = message;
    if (errorMsg.isEmpty()) {
        // 根据错误代码生成默认错误信息
        switch (errorCode) {
            case RepayError_AuthFailed:
                errorMsg = "授权验证失败";
                break;
            case RepayError_VehicleNotFound:
                errorMsg = "车辆信息未找到";
                break;
            case RepayError_NetworkFailed:
                errorMsg = "网络连接失败";
                break;
            case RepayError_AmountExceeded:
                errorMsg = "金额超出限制";
                break;
            default:
                errorMsg = "补费处理出错";
                break;
        }
    }

    ShowErrorMessage(errorMsg);

    // 根据错误类型决定返回的阶段
    if (errorCode == RepayError_AuthFailed) {
        // 授权失败，需要重新开始
    
        EnableInput(true);
    } else if (errorCode == RepayError_VehicleNotFound || errorCode == RepayError_NetworkFailed) {
        // 车辆或网络问题，返回输入阶段
    
        EnableInput(true);
    } else if (errorCode == RepayError_AmountExceeded) {
        // 金额问题，返回金额确认阶段

        EnablePaymentButtons(true);
    } else {
        // 其他错误，返回输入阶段
    
        EnableInput(true);
    }


}

void FormRepayNew::StartPaymentProcess()
{
    // 显示支付方式选择界面
    CTransPayType selectedPayType;
    if (ShowPaymentSelection(selectedPayType)) {
        // 用户选择了支付方式，开始支付处理
        ProcessPayment(selectedPayType);
    } else {
        // 用户取消了支付方式选择，返回金额确认阶段
        InfoLog("用户取消支付方式选择");
        ShowWarningMessage("支付方式选择已取消");

        // 返回到金额确认阶段，允许用户重新选择支付方式

        SetUIEnabled(true);
        EnablePaymentButtons(true);
    
    }
}

void FormRepayNew::StartDebtQuery()
{
    if (!ValidateVehPlate()) {
        ShowErrorMessage("请输入正确的车牌号");
        return;
    }

    InfoLog(QString("开始查询欠费 - 车牌：%1").arg(m_vehPlate));

    SetUIEnabled(false);
    ShowWarningMessage("正在查询欠费信息...");

    // 开始异步查询
    bool queryStarted = QueryDebtDetail();

    if (!queryStarted) {
        ShowErrorMessage("启动查询失败");
        SetUIEnabled(true);
    }
}

void FormRepayNew::ProcessPayment(CTransPayType payType)
{
    if (!ValidateInputs()) {
        return;
    }
    
    if (!m_bInitialized) {
        ShowErrorMessage("补费组件未初始化");
        return;
    }
    
    InfoLog(QString("开始处理支付 - 方式：%1, 金额：%2分").arg(payType).arg(m_amount));
    
    // 保存当前支付方式
    m_currentPayType = payType;
    
    // 根据补费类型处理业务逻辑
    bool processResult = false;
    
    // 直接处理支付
    if (ProcessPaymentInternal(payType)) {
        // 支付成功，生成补费流水
        if (GenerateRepayRecordInternal(m_repayType)) {
            ShowRepaySuccessDialog(payType);
        }
    } else {
        ShowErrorMessage("补费处理失败");
    }
}

bool FormRepayNew::ValidateVehPlate()
{
    // 简单验证车牌号
    if (m_vehPlate.isEmpty()) {
        return false;
    }

    if (m_vehPlate.length() < 7) {
        return false;
    }

    return true;
}

// Slot方法实现
void FormRepayNew::OnVehPlateChanged()
{
    // 车牌输入现在通过FormInputPlate处理，此方法保留为空
}

void FormRepayNew::OnAmountChanged()
{
    // 已改为独立页面输入金额
    return;
}

void FormRepayNew::OnQueryTimeout()
{
    ShowErrorMessage("查询超时，请重试");
    SetUIEnabled(true);
    
    if (m_pQueryTimer) m_pQueryTimer->stop();
}



void FormRepayNew::OnPaymentButtonClicked()
{
    // 由于使用单独的支付选择页面，这个方法不再需要
    InfoLog("支付按钮点击 - 使用单独的支付选择页面");
}



// 移除了所有复刻的车牌输入方法，现在使用FormInputPlate类




bool FormRepayNew::ShowVehTypeInputDialog()
{
    InfoLog("准备显示车型输入对话框");

    // 先隐藏当前界面
    // 业务逻辑类无需隐藏界面

    // 创建车型输入对话框
    FormInputVehType dlgVehType(nullptr);

    InfoLog(QString("显示车型输入对话框 - 当前车型:%1").arg((int)m_vehType));

    // 显示车型输入对话框
    bool result = dlgVehType.InputVehType((int)m_vehType);

    InfoLog(QString("车型输入对话框返回结果：%1").arg(result ? "成功" : "取消/失败"));

    if (result) {
        // 获取输入的车型
        m_vehType = (CVehClass)dlgVehType.GetInputVehType();
        InfoLog(QString("车型输入完成 - 车型:%1").arg((int)m_vehType));
    } else {
        InfoLog("车型输入被取消或失败");
    }

    return result;
}

bool FormRepayNew::ShowRepaySuccessDialog(CTransPayType payType)
{
    InfoLog("准备显示补费成功确认界面");
    
    // 先隐藏当前界面
    // 业务逻辑类无需隐藏界面
    
    // 创建补费成功界面
    FormRepaySuccess dlgSuccess(nullptr);
    
    // 确定补费类型名称
    QString repayTypeName;
    switch (m_repayType) {
        case RepayType_Current:
            repayTypeName = "当趟";
            break;
        case RepayType_Province:
            repayTypeName = "省内名单";
            break;
        default:
            repayTypeName = "特殊";
            break;
    }
    
    // 确定支付方式名称
    QString payTypeName = GetPayTypeName(payType);
    
    InfoLog(QString("显示补费成功界面 - 方式:%1, 车牌:%2, 支付:%3, 金额:%4分")
            .arg(repayTypeName).arg(m_vehPlate).arg(payTypeName).arg(m_amount));
    
    // 显示补费成功界面
    // 期望：仅显示本次补费结果，不倒计时，需按键继续
    bool result = dlgSuccess.ShowRepaySuccess(repayTypeName, m_vehPlate, m_vehPlateColor, 
                                             payTypeName, m_amount, 0);
    
    InfoLog(QString("补费成功界面返回结果：%1").arg(result ? "确认" : "取消"));
    
    return result;
}

void FormRepayNew::ShowRepayCompleteDialog()
{
    InfoLog("显示补费完成界面");
    
    // 先隐藏当前界面
    // 业务逻辑类无需隐藏界面
    
    // 创建补费完成界面
    FormRepayComplete completeDialog(nullptr);
    
    // 显示补费完成界面
    bool result = completeDialog.ShowRepayComplete(m_vehPlate, m_vehType, m_currentPayType, 
                                                  m_amount, m_repayType);
    
    InfoLog(QString("补费完成界面返回结果：%1").arg(result ? "确认" : "自动关闭"));
    
    // 关闭当前补费界面
                // 业务逻辑类不再处理UI关闭
            emit repayCompleted(true, "补费成功");
}

void FormRepayNew::NotifyProvinceCenter()
{
    InfoLog("开始通知省中心补费完成");
    
    if (!m_bInitialized) {
        ShowErrorMessage("补费组件未初始化");
       // ShowRepayCompleteDialog();  // 即使通知失败也显示完成界面
        return;
    }
    
    // 异步通知省中心
    QString oweFee = QString::number(m_debtResult.totalAmount);
    QString wasteId = m_debtResult.debtItems.isEmpty() ? "" : m_debtResult.debtItems.first().passId;
    QString listno = m_debtResult.listno;
    
    // 仅触发异步通知，不在此处显示界面（界面已在支付成功后立即显示）
    NotifyProvinceCenterInternal();
}

bool FormRepayNew::ShowProvinceRepayInfoDialog(const QString &vehPlate, int vehPlateColor, const RepayDebtQueryResult &result)
{
    InfoLog("准备显示省内名单补费信息界面");


    // 创建省内名单补费信息界面
    FormProvinceRepayInfo dlgRepayInfo(GetMainDlg());

    InfoLog(QString("显示省内名单补费信息界面 - 车牌:%1").arg(vehPlate));

    // 显示补费信息界面（异步查询完成后传入查询结果）
    bool dialogResult = dlgRepayInfo.ShowRepayInfo(vehPlate, vehPlateColor, result);

    InfoLog(QString("省内名单补费信息界面返回结果：%1").arg(dialogResult ? "确认" : "取消"));

    if (dialogResult) {
        // 用户确认补费信息，设置补费数据
        m_vehPlate = vehPlate;
        m_vehPlateColor = vehPlateColor;
        m_amount = dlgRepayInfo.GetTotalAmount();
        
        // 设置默认车型（省内名单补费不需要车型输入）
        m_vehType = VC_Car1;  // 默认客1

        InfoLog(QString("用户确认省内名单补费 - 车牌:%1, 总金额:%2分")
                .arg(vehPlate).arg(m_amount));
        
        return true;
    } else {
        InfoLog("用户取消省内名单补费信息");
        return false;
    }
}

bool FormRepayNew::ShowDebtDetailDialog(const RepayDebtQueryResult &result)
{
    InfoLog("准备显示债务详情界面");

    // 检查债务数据有效性
    if (result.debtItems.isEmpty()) {
        ShowWarningMessage("未查询到有效的欠费记录");
        return false;
    }

    // 先隐藏当前界面，符合项目规范
    // 业务逻辑类无需隐藏界面

    // 使用简化的省内追缴名单明细界面
    FormDebtDetailSimple dlgDebtDetail(nullptr);
    dlgDebtDetail.InitUI();

    InfoLog(QString("显示债务详情界面 - 车牌:%1, 记录数:%2, 总金额:%3分")
            .arg(result.vehiclePlate.isEmpty() ? m_vehPlate : result.vehiclePlate)
            .arg(result.debtItems.size())
            .arg(result.totalAmount));

    // 显示债务详情界面
    bool dialogResult = dlgDebtDetail.ShowDebtDetail(result);

    InfoLog(QString("债务详情界面返回结果：%1").arg(dialogResult ? "成功" : "取消"));

    if (dialogResult) {
        // 用户确认债务详情，使用总金额进行补费
        int totalAmount = dlgDebtDetail.GetTotalRepayAmount();
        
        if (totalAmount <= 0) {
            InfoLog("债务总金额无效");
            ShowWarningMessage("债务总金额无效");
            return false;
        }

        // 更新当前补费信息为总金额
        m_amount = totalAmount;
        
        // 保持原始债务结果不变（包含所有债务项目）
        m_debtResult = result;
        
        InfoLog(QString("用户确认债务补费 - 总金额:%1分, 债务项目数:%2")
                .arg(totalAmount)
                .arg(result.debtItems.size()));
        
        // 更新界面显示的债务信息
        UpdateDebtInfo(m_debtResult);
        
        return true;
    } else {
        InfoLog("用户取消债务详情选择");
        return false;
    }
}

bool FormRepayNew::ShowPaymentSelection(CTransPayType &selectedPayType)
{
    InfoLog("显示支付方式选择界面");
    
    // 创建支付方式选择界面
    FormPaymentSelect paymentDialog;

    paymentDialog.InitUI();
    
    // 设置可用的支付方式 - 重新定义支付方式含义
    QList<CTransPayType> availablePayTypes;
    availablePayTypes << TransPT_Cash;
    availablePayTypes << TransPT_Union;     // 银联卡代表移动支付（支付宝/微信）
    availablePayTypes << TransPT_ETCCard;   // 赣通卡包含ETC
    
    paymentDialog.SetAvailablePayTypes(availablePayTypes);
    
    // 显示选择界面
    return paymentDialog.ShowPaymentSelect(selectedPayType);
}

void FormRepayNew::FillTransInfo(CTransPayType payType)
{
    // 参照formrepay.cpp的FillTransInfo方法实现
    QDateTime curTime = QDateTime::currentDateTime();

    m_curTransInfo.ClearTransInfo();
    
    // 获取门架信息
    QString sGantryHex;
    bool bOpengantry = false;
    CTollGantryMgr::GetTollGantryMgr()->GetCurGantryInfo(sGantryHex, m_curTransInfo.m_curGantryInfo, bOpengantry);
    
    // 设置交易时间
    m_curTransInfo.TransTime = curTime;
    
    // 设置车辆信息
    CVehInfo vehInfo;
    memset(&vehInfo, 0, sizeof(CVehInfo));
    
    // 将车牌号转换为字符数组
    QByteArray plateBytes = m_vehPlate.toLocal8Bit();
    strncpy(vehInfo.szVehPlate, plateBytes.constData(), sizeof(vehInfo.szVehPlate) - 1);
    vehInfo.szVehPlate[sizeof(vehInfo.szVehPlate) - 1] = '\0';
    
    vehInfo.nVehPlateColor = m_vehPlateColor;
    vehInfo.VehClass = m_vehType;
    
    m_curTransInfo.SetVehInfo(&vehInfo);
    
    // 设置费用信息
    m_curTransInfo.m_nTotalFee = m_amount;
    m_curTransInfo.m_nTransFee = m_amount;
    m_curTransInfo.m_nProvFee = m_amount;
    m_curTransInfo.m_nProvinceDiscountFee = 0;
    m_curTransInfo.m_nOriginFee = 0;
    m_curTransInfo.m_nDiscountFee = 0;
    m_curTransInfo.m_nProvinceCount = 1;
    m_curTransInfo.m_nRepayType = static_cast<int>(m_repayType);

    // 设置门架费用信息
    CGantryFeeInfo gantryFeeInfo;
    gantryFeeInfo.Clear();
    gantryFeeInfo.payFee = m_amount;
    gantryFeeInfo.discountFee = 0;
    gantryFeeInfo.realFee = m_amount;
    gantryFeeInfo.cardFee = m_amount;
    
    InfoLog(QString("填充交易信息完成 - 车牌:%1, 车型:%2, 金额:%3分, 支付方式:%4")
            .arg(m_vehPlate).arg(m_vehType).arg(m_amount).arg(payType));
}

bool FormRepayNew::GenerateRepayRecord(CTransPayType payType)
{
    // 填充交易信息
    FillTransInfo(payType);
    
    // 完成交易
    m_curTransInfo.CompleteTrans(0, payType, NULL, Tr_Successed);
    
    // 保存补费流水（参照formrepay.cpp的CompleteRepay方法）
    bool bResult = Ptr_ETCCtrl->saveTransWaste_Repay(&m_curTransInfo);
    
    if (bResult) {
        QString payTypeName = GetPayTypeName(payType);
        InfoLog(QString("补费流水保存成功 - 车辆:%1, %2补费成功").arg(m_vehPlate).arg(payTypeName));
    } else {
        ErrorLog(QString("补费流水保存失败 - 车辆:%1").arg(m_vehPlate));
    }
    
    return bResult;
}

QString FormRepayNew::GetPayTypeName(CTransPayType payType)
{
    // 参照formrepay.cpp的实现
    switch (payType) {
        case TransPT_OBU:
        case TransPT_ETCCard:
            return QString("ETC 刷卡");
        case TransPT_Cash:
            return QString("现金");
        case TransPT_AliPay:
            return QString("支付宝");
        case TransPT_WeChat:
            return QString("微信");
        case TransPT_Union:
            return QString("银联卡");
        case TransPT_Other:
            return QString("第三方支付");
        default:
            return QString("未知支付方式");
    }
}

// ===== 从RepayManager移植过来的核心业务方法 =====

bool FormRepayNew::InitializeRepayComponents()
{
    if (m_bInitialized) {
        return true;
    }
    
    try {
        InfoLog("初始化补费组件");
        
        // 获取RepayConfig实例
        m_pRepayConfig = RepayConfig::GetInstance();
        if (!m_pRepayConfig) {
            ErrorLog("RepayConfig初始化失败");
            return false;
        }
        
        // 初始化省中心接口
        m_pProDebPayment = ProDebPayment::GetProDebPayment();
        if (!m_pProDebPayment) {
            m_pProDebPayment->SetOrgInfo(Ptr_Info->GetGBStationId(),Ptr_Info->GetGBLaneId(),Ptr_Info->GetStationName());
            ErrorLog("省中心接口初始化失败");
            return false;
        }
        
        // 初始化授权管理器
        m_pAuthManager = AuthManager::GetInstance();
        if (!m_pAuthManager) {
            ErrorLog("授权管理器初始化失败");
            return false;
        }
        
        m_bInitialized = true;
        InfoLog("补费组件初始化完成");
        
        return true;
    }
    catch (...) {
        ErrorLog("补费组件初始化异常");
        return false;
    }
}

bool FormRepayNew::ProcessCurrentRepayFlow()
{
    InfoLog("开始当趟补费流程");
    
//    // 1. 车型输入界面
//    if (!ShowVehTypeInputDialog()) {
//        InfoLog("用户取消车型选择");
//        return false;
//    }
    
//    // 2. 车牌输入界面
//    if (!ShowPlateInputDialog()) {
//        InfoLog("用户取消车牌输入");
//        return false;
//    }
    
    // 3. 补费金额输入界面
    if (!ShowAmountInputDialog()) {
        InfoLog("用户取消金额输入");
        return false;
    }
    
    // 4. 验证补费金额
    if (!ValidateAmount()) {
        return false;
    }
    
    // 5. 支付方式选择界面
    CTransPayType selectedPayType;
    if (!ShowPaymentSelection(selectedPayType)) {
        InfoLog("用户取消支付方式选择");
        return false;
    }
    
    // 6. 处理支付

    if (!ProcessPaymentInternal(selectedPayType)) {
        ShowErrorMessage("支付处理失败");
        return false;
    }
    
    // 7. 生成补费流水
    if (!GenerateRepayRecordInternal(RepayType_Current)) {
        ShowErrorMessage("生成补费流水失败");
        return false;
    }
    
    // 8. 显示补费成功界面
    if (ShowRepaySuccessDialog(selectedPayType)) {
        OnRepayCompleted(true, "当趟补费完成");
        return true;
    }
    
    return false;
}

// 确认车辆是否可以当趟补费
bool FormRepayNew::ConfirmVehicleForCurrentRepay()
{
    InfoLog(QString("开始确认车辆当趟补费 - 车牌:%1, 颜色:%2, 金额:%3分")
            .arg(m_vehPlate).arg(m_vehPlateColor).arg(m_amount));
    
    if (!m_pProDebPayment) {
        ErrorLog("ProDebPayment组件未初始化");
        ShowErrorMessage("系统组件未初始化");
        return false;
    }
    
    // 验证输入参数
    if (m_vehPlate.isEmpty()) {
        ErrorLog("车牌号为空");
        ShowErrorMessage("请先输入车牌号");
        return false;
    }
    
    if (m_amount <= 0) {
        ErrorLog("补费金额无效");
        ShowErrorMessage("请先输入正确的补费金额");
        return false;
    }
    
    // sOweFee单位是分，直接将金额转换为字符串
    QString sOweFee = QString::number(m_amount);
    
    SetUIEnabled(false);
    ShowWarningMessage("正在确认车辆补费信息...");
    
    // 调用异步确认接口
    m_pProDebPayment->ConfirmCurrentDebtAsync(m_vehPlate, m_vehPlateColor, sOweFee);
    
    InfoLog(QString("已发送当趟补费确认请求 - 车牌:%1, 金额:%2分").arg(m_vehPlate).arg(sOweFee));
    
    return true;
}

// 处理当趟补费确认结果
void FormRepayNew::OnCurrentDebtConfirmed(bool success, const CurrentDebtConfirmResult &result)
{
    InfoLog(QString("收到当趟补费确认结果 - 成功:%1").arg(success ? "是" : "否"));
    
    SetUIEnabled(true);
    
    if (success) {
        InfoLog("车辆当趟补费确认成功，继续支付流程");
        ShowSuccessMessage("车辆确认成功");
        
        // 延迟一下再进入支付流程，让用户看到成功信息
        QTimer::singleShot(1500, this, SLOT(StartPaymentProcess()));
        
    } else {
        QString errorMsg = QString("车辆确认失败");
//        if (!result.errorMessage.isEmpty()) {
//            errorMsg += QString(": %1").arg(result.errorMessage);
//        }
        
        ErrorLog(errorMsg);
        ShowErrorMessage(errorMsg);
        
        // 确认失败，返回到输入阶段，允许用户重新输入
        EnableInput(true);
        emit repayCompleted(false, errorMsg);
    }
}

bool FormRepayNew::ProcessProvinceRepayFlow()
{
    InfoLog("开始省内名单补费流程");
    
    // 省内名单补费直接使用当前车辆信息，不需要车牌输入界面
    InfoLog(QString("使用当前车辆信息 - 车牌:%1, 颜色:%2").arg(m_vehPlate).arg(m_vehPlateColor));
    
    // 1. 直接查询欠费明细

    if (!QueryDebtDetail()) {
        ShowErrorMessage("查询欠费明细失败");
        return false;
    }
    
    // 等待异步查询完成，在OnDebtQueryFinished中继续流程
    return true;
}

bool FormRepayNew::QueryDebtDetail()
{
    // Debug 调试模式：支持通过环境变量 MOCK_PROVINCE_DEBT=1 模拟查询结果
#ifdef QT_DEBUG
    //QByteArray mockFlag = char("1");//qgetenv("MOCK_PROVINCE_DEBT");
    //if (mockFlag == "1")
    {
        InfoLog("[DEBUG] 使用模拟的省内欠费查询结果 (MOCK_PROVINCE_DEBT=1)");

        RepayDebtQueryResult result;
        result.vehiclePlate = m_vehPlate;
        result.vehiclePlateColor = m_vehPlateColor;
        result.totalAmount = 12345 + 6789 + 4500; // 三条合计（分）
        result.queryTime = QDateTime::currentDateTime().toString("yyyyMMddhhmmss");

        // 第1条
        RepayDebtItem item1;
        item1.orderIds = "MOCK001|MOCK002";
        item1.debtDate = QDateTime::currentDateTime().addDays(-1).toString("yyyy-MM-dd hh:mm:ss");
        item1.entryStation = QString::fromUtf8("测试入口站A");
        item1.exitStation = QString::fromUtf8("测试出口站A");
        item1.debtAmount = 12345; // 123.45元
        item1.passId = "TESTPASSID-A";
        item1.remark = QString::fromUtf8("调试模式模拟数据-1");
        result.debtItems.append(item1);

        // 第2条
        RepayDebtItem item2;
        item2.orderIds = "MOCK003";
        item2.debtDate = QDateTime::currentDateTime().addDays(-2).toString("yyyy-MM-dd hh:mm:ss");
        item2.entryStation = QString::fromUtf8("测试入口站B");
        item2.exitStation = QString::fromUtf8("测试出口站B");
        item2.debtAmount = 6789; // 67.89元
        item2.passId = "TESTPASSID-B";
        item2.remark = QString::fromUtf8("调试模式模拟数据-2");
        result.debtItems.append(item2);

        // 第3条
        RepayDebtItem item3;
        item3.orderIds = "MOCK004";
        item3.debtDate = QDateTime::currentDateTime().addDays(-3).toString("yyyy-MM-dd hh:mm:ss");
        item3.entryStation = QString::fromUtf8("测试入口站C");
        item3.exitStation = QString::fromUtf8("测试出口站C");
        item3.debtAmount = 4500; // 45.00元
        item3.passId = "TESTPASSID-C";
        item3.remark = QString::fromUtf8("调试模式模拟数据-3");
        result.debtItems.append(item3);

        // 直接走异步完成后的处理逻辑
        OnDebtQueryFinished(true, result);
        return true;
    }
#endif

    if (!m_pProDebPayment) {
        ErrorLog("省中心接口未初始化");
        return false;
    }

    InfoLog(QString("查询省内欠费明细 - 车牌:%1").arg(m_vehPlate));

    // 异步查询，由 OnDebtQueryFinished 继续后续流程
    m_pProDebPayment->QueryDebtInfoAsync(m_vehPlate, m_vehPlateColor);
    InfoLog(QString("已启动欠费查询 - 车牌:%1").arg(m_vehPlate));
    return true;
}

bool FormRepayNew::ProcessPaymentInternal(CTransPayType payType)
{
    InfoLog(QString("处理补费支付 - 车牌:%1, 支付方式:%2, 金额:%3分")
            .arg(m_vehPlate).arg(payType).arg(m_amount));
    
    // 生成流水ID
    m_currentWasteId = GenerateWasteId();
    
    // 存储支付参数
    m_currentPayType = payType;
    
    // 根据支付方式调用相应的处理逻辑
    bool paymentResult = false;
    QString errorMessage;
    
    switch (payType) {
        case TransPT_Cash:
            paymentResult = ProcessCashPayment(m_amount, m_vehPlate, m_vehPlateColor, errorMessage);
            break;
            
        case TransPT_Union:
        case TransPT_AliPay:
        case TransPT_WeChat:
            paymentResult = ProcessMobilePayment(payType, m_amount, m_vehPlate, m_vehPlateColor, errorMessage);
            break;
            
        case TransPT_ETCCard:
            paymentResult = ProcessETCPayment(m_amount, m_vehPlate, m_vehPlateColor, errorMessage);
            break;
            
        default:
            errorMessage = QString("不支持的支付方式：%1").arg(payType);
            ErrorLog(errorMessage);
            paymentResult = false;
            break;
    }
    
    // 处理支付结果
    if (paymentResult) {
        HandlePaymentSuccess(payType, m_amount);
        return true;
    } else {
        HandlePaymentFailure(errorMessage);
        return false;
    }
}

bool FormRepayNew::GenerateRepayRecordInternal(RepayType repayType, const QString &orderIds)
{
    InfoLog(QString("生成补费流水 - 类型:%1, 车牌:%2, 订单:%3")
            .arg(GetRepayTypeName(repayType))
            .arg(m_vehPlate)
            .arg(orderIds));
    
    // 构造完整的交易信息
    CTransInfo completeTransInfo;
    FillTransInfoInternal(completeTransInfo, m_vehPlate, m_vehPlateColor, 
                         m_vehType, m_amount, repayType, m_currentPayType);
    
    // 调用ETC控制器保存补费流水 - 这是关键的流水保存逻辑
    bool bRlt = Ptr_ETCCtrl->saveTransWaste_Repay(&completeTransInfo);
    
    if (bRlt) {
        InfoLog(QString("补费流水保存成功 - 车牌:%1, 金额:%2分").arg(m_vehPlate).arg(m_amount));
        
        // 记录补费操作到本地日志
        LogRepayOperation(repayType, m_vehPlate, m_vehPlateColor,
                         m_amount, m_currentPayType, true, "", orderIds, 
                         m_currentListno, m_currentWasteId);
    } else {
        ErrorLog(QString("补费流水保存失败 - 车牌:%1, 金额:%2分").arg(m_vehPlate).arg(m_amount));
        
        // 记录失败操作
        LogRepayOperation(repayType, m_vehPlate, m_vehPlateColor,
                         m_amount, m_currentPayType, false, "流水保存失败", orderIds, 
                         m_currentListno, m_currentWasteId);
    }
    
    return bRlt;
}

bool FormRepayNew::ValidateRepayConditions(RepayType type, const QString &vehPlate, int vehPlateColor)
{
    // 先验证补费类型
    if (type == RepayType_None) {
        ErrorLog("补费类型无效");
        ShowErrorMessage("补费类型无效");
        return false;
    }

    // 再按补费方式验证车辆信息：当趟补费跳过，省内名单补费验证
    if (!ValidateVehicleInfoInternal(vehPlate, vehPlateColor)) {
        return false;
    }

    InfoLog(QString("补费条件验证通过 - 类型:%1, 车牌:%2")
            .arg(GetRepayTypeName(type)).arg(vehPlate));

    return true;
}

bool FormRepayNew::ValidateVehicleInfoInternal(const QString &vehPlate, int vehPlateColor)
{
    // 按补费方式判断：当趟补费不验证，省内名单补费验证
//    if (m_repayType == RepayType_Current) {
//        InfoLog("当趟补费：跳过车辆信息验证");
//        return true;
//    }

    // 省内名单补费：执行车辆信息验证
    if (vehPlate.isEmpty()) {
        WarnLog("车牌号为空");
        ShowErrorMessage("车牌号不能为空");
        return false;
    }

    if (vehPlateColor < 0 || vehPlateColor > 9) {
        WarnLog(QString("车牌颜色无效：%1").arg(vehPlateColor));
        ShowErrorMessage("车牌颜色无效");
        return false;
    }

    DebugLog(QString("车辆信息验证通过 - 车牌:%1, 颜色:%2").arg(vehPlate).arg(vehPlateColor));
    return true;
}

QString FormRepayNew::GenerateWasteId()
{
    // 生成格式：YYYYMMDDHHMMSS + 3位随机数
    QDateTime now = QDateTime::currentDateTime();
    QString timestamp = now.toString("yyyyMMddhhmmss");
    int random = qrand() % 1000;
    QString wasteId = QString("%1%2").arg(timestamp).arg(random, 3, 10, QChar('0'));
    
    DebugLog(QString("生成流水ID：%1").arg(wasteId));
    return wasteId;
}

void FormRepayNew::HandlePaymentSuccess(CTransPayType payType, int amount)
{
    InfoLog(QString("支付成功 - 方式:%1, 金额:%2分").arg(payType).arg(amount));

}

void FormRepayNew::HandlePaymentFailure(const QString &errorMsg)
{
    ErrorLog(QString("支付失败：%1").arg(errorMsg));
    ShowErrorMessage(QString("支付失败：%1").arg(errorMsg));
    OnRepayError(RepayError_PaymentFailed, errorMsg);
}

void FormRepayNew::FillTransInfoInternal(CTransInfo &transInfo, const QString &vehPlate, int vehPlateColor, 
                                        int vehType, int amount, RepayType repayType, CTransPayType payType)
{
    // 基础车辆信息
    QByteArray plateBytes = vehPlate.toLocal8Bit();
    qstrncpy(transInfo.VehInfo.szVehPlate, plateBytes.data(), sizeof(transInfo.VehInfo.szVehPlate));
    transInfo.VehInfo.nVehPlateColor = (quint8)vehPlateColor;
    transInfo.VehInfo.VehClass = (CVehClass)vehType;
    
    // 支付信息与金额
    transInfo.m_transPayType = payType;
    transInfo.m_nTransFee = amount;                 // 实收金额（分）
    
    // 补费标识
    transInfo.m_bRepay = true;
    transInfo.m_nRepayType = (repayType == RepayType_Current) ? 1 : 2; // 1=当趟补费, 2=省内名单补费
    
    // 时间信息与交易金额时间戳
    QDateTime now = QDateTime::currentDateTime();
    transInfo.SetConsumeMoney(amount, now);         // 同步内部ConsumeMoney与TransTime
    
    // 流水ID
    transInfo.m_sId = m_currentWasteId;
    
    InfoLog(QString("填充交易信息完成 - 车牌:%1, 金额:%2分, 类型:%3")
            .arg(vehPlate).arg(amount).arg(repayType));
}

void FormRepayNew::LogRepayOperation(RepayType type, const QString &vehPlate, int vehPlateColor,
                                    int amount, CTransPayType payType, bool success, 
                                    const QString &errorMsg, const QString &orderIds,
                                    const QString &listno, const QString &wasteId)
{
    QString logMsg = QString("补费操作 - 类型:%1, 车牌:%2, 金额:%3分, 支付:%4, 结果:%5")
                     .arg(GetRepayTypeName(type))
                     .arg(vehPlate)
                     .arg(amount)
                     .arg(payType)
                     .arg(success ? "成功" : "失败");
    
    if (!success && !errorMsg.isEmpty()) {
        logMsg += QString(", 错误:%1").arg(errorMsg);
    }
    if (!orderIds.isEmpty()) {
        logMsg += QString(", 订单:%1").arg(orderIds);
    }
    if (!wasteId.isEmpty()) {
        logMsg += QString(", 流水:%1").arg(wasteId);
    }
    
    if (success) {
        InfoLog(logMsg);
    } else {
        ErrorLog(logMsg);
    }
}

// 支付方式处理实现（简化版本）
bool FormRepayNew::ProcessCashPayment(int amount, const QString &vehPlate, int vehPlateColor, QString &errorMessage)
{
    InfoLog(QString("处理现金支付 - 金额:%1分").arg(amount));
    // 现金支付通常直接成功
    return true;
}

bool FormRepayNew::ProcessMobilePayment(CTransPayType payType, int amount, const QString &vehPlate, int vehPlateColor, QString &errorMessage)
{
    InfoLog(QString("处理移动支付 - 车牌:%1, 支付方式:%2, 金额:%3分")
            .arg(vehPlate).arg(payType).arg(amount));
    
    try {
        // 1. 验证支付参数
        if (amount <= 0) {
            errorMessage = "支付金额无效";
            return false;
        }
        
        // 2. 确定移动支付类别（具体支付类型在用户扫码后确定）
        FormMobilePayBase::PayClass payClass = FormMobilePayBase::PayClass_Mobile;
        
        InfoLog("开始移动支付处理，等待用户选择支付方式");
        
        // 3. 创建临时交易信息用于支付
        CTransInfo tempTransInfo;
        FillTransInfoInternal(tempTransInfo, vehPlate, vehPlateColor, m_vehType, amount, m_repayType, payType);
        
        // 4. 检查设备状态
        if (!Ptr_Info->bHaveCardMgr()) {
            // 如果没有卡机，使用特微扫码设备
            if (CDeviceFactory::GetSpEventDev()->IsExist()) {
                if (CDeviceFactory::GetSpEventDev()->IsInitSucc()) {
                    FormMobilePayTwDev dlgPayTwDev(nullptr);
                    CTransPayType resultPayType;
                    bool payResult = dlgPayTwDev.Pay(resultPayType, payClass, &tempTransInfo);
                    
                    if (payResult) {
                        // 根据实际支付类型记录日志
                        QString actualPayTypeName = GetActualPayTypeName(resultPayType);
                        InfoLog(QString("%1支付(特微设备)处理成功").arg(actualPayTypeName));
                        
                        // 更新支付结果到交易信息
                        if (resultPayType == TransPT_WeChat || resultPayType == TransPT_AliPay || 
                            resultPayType == TransPT_Union || resultPayType == TransPT_Other) {
                            m_curTransInfo.m_payCode = tempTransInfo.m_payCode;
                            m_curTransInfo.m_payOrderNum = tempTransInfo.m_payOrderNum;
                            m_curTransInfo.m_nPayChannel = tempTransInfo.m_nPayChannel;
                        }
                        return true;
                    } else {
                        errorMessage = "移动支付被取消或失败";
                        return false;
                    }
                } else {
                    errorMessage = "壁挂扫码设备异常，无法支付";
                    ErrorLog(errorMessage);
                    return false;
                }
            } else {
                errorMessage = "自助扫码被禁用，无法支付";
                ErrorLog(errorMessage);
                return false;
            }
        } else {
            // 使用标准移动支付流程
            FormMobilePay dlgPay(nullptr);
            CTransPayType resultPayType;
            bool payResult = dlgPay.Pay(resultPayType, payClass, &tempTransInfo);
            
            if (payResult) {
                // 根据实际支付类型记录日志
                QString actualPayTypeName = GetActualPayTypeName(resultPayType);
                InfoLog(QString("%1支付处理成功").arg(actualPayTypeName));
                
                // 更新支付结果到交易信息
                if (resultPayType == TransPT_WeChat || resultPayType == TransPT_AliPay || 
                    resultPayType == TransPT_Union || resultPayType == TransPT_Other) {
                    m_curTransInfo.m_payCode = tempTransInfo.m_payCode;
                    m_curTransInfo.m_payOrderNum = tempTransInfo.m_payOrderNum;
                    m_curTransInfo.m_nPayChannel = tempTransInfo.m_nPayChannel;
                }
                return true;
            } else {
                errorMessage = "移动支付被取消或失败";
                return false;
            }
        }
        
    } catch (const std::exception &e) {
        errorMessage = QString("移动支付处理异常：%1").arg(e.what());
        ErrorLog(errorMessage);
        return false;
    }
}

bool FormRepayNew::ProcessETCPayment(int amount, const QString &vehPlate, int vehPlateColor, QString &errorMessage)
{
    InfoLog(QString("处理ETC卡支付 - 车牌:%1, 金额:%2分").arg(vehPlate).arg(amount));
    
    try {
        // 1. 验证支付参数
        if (amount <= 0) {
            errorMessage = "支付金额无效";
            return false;
        }
        
        // 2. 检查ETC读卡设备状态
        if (!CDeviceFactory::GetCardReader(DevIndex_Manual)) {
            errorMessage = "ETC读卡设备不可用";
            ErrorLog(errorMessage);
            return false;
        }
        
        // 3. 准备支付信息（填充当前交易信息）
        FillTransInfoInternal(m_curTransInfo, vehPlate, vehPlateColor, m_vehType, amount, m_repayType, TransPT_ETCCard);
        
        InfoLog("开始ETC卡支付处理");
        
        // 4. 调用ETC支付对话框（它会通过GetCurTransInfo获取当前交易信息）
        FormEtcPay dlgEtcPay(GetMainDlg());
        bool payResult = dlgEtcPay.Pay();
        
        if (payResult) {
            InfoLog("ETC卡支付处理成功");
            
            // ETC支付成功后，交易信息已经在m_curTransInfo中更新了
            // 检查是否有ETC卡信息
            if (!m_curTransInfo.IccInfo.ProCardBasicInfo.szCardNo[0]) {
                DebugLog("警告：ETC支付成功但未获取到卡信息");
            } else {
                InfoLog(QString("ETC卡支付成功 - 卡号:%1")
                        .arg(QString::fromAscii(m_curTransInfo.IccInfo.ProCardBasicInfo.szCardNo)));
            }
            
            return true;
        } else {
            errorMessage = "ETC卡支付被取消或失败";
            InfoLog(errorMessage);
            return false;
        }
        
    } catch (const std::exception &e) {
        errorMessage = QString("ETC卡支付处理异常：%1").arg(e.what());
        ErrorLog(errorMessage);
        return false;
    }
}

// 获取实际支付类型名称
QString FormRepayNew::GetActualPayTypeName(CTransPayType payType)
{
    switch (payType) {
        case TransPT_AliPay:
            return "支付宝";
        case TransPT_WeChat:
            return "微信";
        case TransPT_Union:
            return "银联卡";
        case TransPT_Cash:
            return "现金";
        case TransPT_ETCCard:
            return "ETC卡";
        case TransPT_Other:
            return "其他";
        default:
            return QString("未知支付方式(%1)").arg(payType);
    }
}

// 新增的信号处理槽函数
void FormRepayNew::OnDebtQueryFinished(bool success, const RepayDebtQueryResult &result)
{
    if (success && ValidateProvinceRepayResult(result)) {
        m_debtResult = result;
        
        // 1. 显示补费信息展示界面
        if (!ShowProvinceRepayInfoDialog(m_vehPlate, m_vehPlateColor, result)) {
            InfoLog("用户取消查看补费信息");
            OnRepayCompleted(false, "用户取消操作");
            return;
        }
        
        // 2. 使用已设置的拦截方式（在补费类型选择后已经选择）
        m_currentListno = result.listno;
        m_amount = result.totalAmount;
        
        InfoLog(QString("省内名单补费信息确认 - 拦截方式:%1, 工单号:%2, 金额:%3分")
                .arg(m_currentInterceptType == Intercept_Entry ? "入口拦截" : "出口拦截")
                .arg(m_currentListno).arg(m_amount));
            
        
        // 3. 继续补费流程 - 支付方式选择
        CTransPayType selectedPayType;
        if (ShowPaymentSelection(selectedPayType)) {
            // 处理支付
        
            if (ProcessPaymentInternal(selectedPayType)) {
                // 生成补费流水
                                    // 从欠费明细中提取订单ID
                    QString orderIds;
                    for (int i = 0; i < result.debtItems.size(); i++) {
                        if (!orderIds.isEmpty()) {
                            orderIds += "|";
                        }
                        orderIds += result.debtItems[i].orderIds;
                    }
                    
                    if (GenerateRepayRecordInternal(RepayType_Province, orderIds)) {
                        // 异步通知省中心（不阻塞、不依赖返回值）
                        ShowRepaySuccessDialog(selectedPayType);
                        NotifyProvinceCenterInternal();
                        // 立即显示补费完成确认界面
                       // ShowRepayCompleteDialog();
                        //ShowRepaySuccessDialog(selectedPayType);
                }
            }
        } else {
            InfoLog("用户取消支付方式选择");
            OnRepayCompleted(false, "用户取消操作");
        }
    } else {
        ShowErrorMessage("查询欠费明细失败");
        OnRepayCompleted(false, "查询欠费明细失败");
    }
}

bool FormRepayNew::ValidateProvinceRepayResult(const RepayDebtQueryResult &result)
{
    if (result.debtItems.isEmpty()) {
        WarnLog("省内欠费查询结果为空");
        return false;
    }
    
    if (result.totalAmount <= 0) {
        WarnLog(QString("省内欠费金额无效：%1分").arg(result.totalAmount));
        return false;
    }
    
    InfoLog(QString("省内欠费查询结果验证通过 - 工单数:%1, 总金额:%2分")
            .arg(result.debtItems.size()).arg(result.totalAmount));
    return true;
}

bool FormRepayNew::NotifyProvinceCenterInternal()
{
    if (!m_pProDebPayment) {
        ErrorLog("省中心接口未初始化");
        return false;
    }
    
    QString oweFee = QString::number(m_amount / 100.0, 'f', 2);
    
    InfoLog(QString("通知省中心补费完成 - 车牌:%1, 金额:%2元").arg(m_vehPlate).arg(oweFee));
    
    // 异步通知不返回结果，调用后直接认为已启动，由回调处理最终结果
    m_pProDebPayment->NotifyDebtCompleteAsync(m_vehPlate, m_vehPlateColor,
                                              oweFee, m_currentWasteId, m_currentListno);
    InfoLog("省中心通知已启动");
    return true;
}

void FormRepayNew::OnAuthorizationCompleted(bool success)
{
    if (success) {
        InfoLog("授权验证成功");
        // 继续补费流程
    } else {
        ErrorLog("授权验证失败");
        ShowErrorMessage("授权验证失败，补费流程已取消");
        OnRepayCompleted(false, "授权验证失败");
    }
}

void FormRepayNew::OnNotifyCompleteFinished(bool success, const QString &result)
{
    if (success) {
        InfoLog(QString("省中心通知成功：%1").arg(result));
    } else {
        WarnLog(QString("省中心通知失败：%1").arg(result));
        // 即使通知失败，补费流程仍然算成功
    }
    
    // 省中心通知结果仅记录日志，不再显示界面（界面已在支付成功后立即显示）
    InfoLog(QString("省中心通知完成，结果已记录"));
}
