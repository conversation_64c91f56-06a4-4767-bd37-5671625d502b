#ifndef AUTHMANAGER_H
#define AUTHMANAGER_H

#include <QObject>
#include <QTimer>
#include <QDateTime>
#include <QMutex>
#include "../../common/paramfilemgr.h"
#include "../../common/paramfile.h"
#include "../../laneinfo.h"

/**
 * @brief 授权管理器
 * 负责处理补费操作的班长授权验证
 * 包含密码验证、权限检查、会话管理等功能
 */
class AuthManager : public QObject
{
    Q_OBJECT
    
public:
    static AuthManager* GetInstance();
    
    // 验证操作员授权
    bool ValidateOperator(const QString &operatorId, const QString &password);
    
    // 检查班长权限
    bool CheckManagerPermission(const QString &operatorId);
    
    // 授权时效性检查
    bool IsAuthorizationValid();
    
    // 开始授权会话
    void StartAuthSession(const QString &operatorId);
    
    // 结束授权会话
    void EndAuthSession();
    
    // 获取当前授权操作员
    QString GetCurrentAuthorizedOperator() const;
    
    // 获取授权剩余时间（秒）
    int GetRemainingAuthTime() const;
    
    // 延长授权时间
    void ExtendAuthSession();
    
    // 检查密码强度
    bool ValidatePasswordStrength(const QString &password);
    
    // 获取授权配置
    void LoadAuthConfig();

signals:
    // 授权开始信号
    void AuthSessionStarted(const QString &operatorId);
    
    // 授权结束信号
    void AuthSessionEnded();
    
    // 授权即将超时信号（提前1分钟）
    void AuthSessionWarning();
    
    // 授权超时信号
    void AuthSessionTimeout();

private slots:
    void OnAuthTimeout();
    void OnAuthWarning();

private:
    AuthManager(QObject *parent = 0);
    
    // 记录授权操作
    void LogAuthOperation(const QString &operatorId, bool success, const QString &reason = "");
    
private:
    static AuthManager *m_pInstance;
    static QMutex m_mutex;
    
    QString m_sCurrentOperator;     // 当前授权操作员
    QDateTime m_authStartTime;      // 授权开始时间
    QTimer *m_pAuthTimer;          // 授权超时定时器
    QTimer *m_pWarningTimer;       // 授权警告定时器
    bool m_bAuthorized;            // 是否已授权
    
    // 配置参数
    int m_nAuthTimeoutSecs;        // 授权超时时间（秒）
    int m_nMaxRetryTimes;          // 最大重试次数
    bool m_bRequireAuth;           // 是否需要授权
    
    // 重试计数
    QMap<QString, int> m_retryCountMap;  // 操作员重试次数记录
    QMap<QString, QDateTime> m_lockTimeMap;  // 操作员锁定时间记录
};

#endif // AUTHMANAGER_H 