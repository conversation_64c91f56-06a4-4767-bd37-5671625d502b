#include "wtsysdev_zc.h"
#include <QDir>
#include "log4qt.h"

#include "globalutils.h"
#include "etclanectrl.h"
#include "transinfo.h"
#include "devicefactory.h"


bool CWtSysDev_ZC::m_bDriverLoaded = false;
QLibrary CWtSysDev_ZC::m_hLibModule;

CWtSysDev_ZC::CWtSysDev_ZC()
{
    InfoLog(QString("称重初始化接口：WtSysDev_ZC"));
    // 初始化成员变量
    m_bDevInited = false;
    m_LastCount = -1;
    m_nWtStatus = 0;
    
    // 初始化函数指针
    WtSys_Init = NULL;
    WtSys_Test = NULL;
    WtSys_SetCom = NULL;
    WtSys_CloseCom = NULL;
    WtSys_ClearOne = NULL;
    WtSys_GetVehicleCount = NULL;
    WtSys_GetAxisCount = NULL;
    WtSys_GetAxisData = NULL;
    WtSys_ManualFinishing = NULL;
    WtSys_OpenManual = NULL;
    WtSys_CloseManual = NULL;
    WtSys_IsVehicleAlone = NULL;
    WtSys_UploadVehicleAgain = NULL;
    WtSys_GetDevParameter = NULL;
    WtSys_GetFactoryInfo = NULL;
    m_wtSysDevFinish = NULL;
    
    // 初始化标记
    m_hasDevFinish = false;
}

CWtSysDev_ZC::~CWtSysDev_ZC()
{
    if (m_bDevInited) {
        CloseDev();
    }
    if (m_bDriverLoaded) {
        ReleaseDriver();
    }
}

bool CWtSysDev_ZC::StartDev()
{
    if (!m_bDriverLoaded) {
        ChangeStatus(DEV_STATUS_LibErr);
        return false;
    }

    int nComId = m_sConnStr1.mid(m_sConnStr1.toUpper().indexOf("M") + 1).toInt();
    int nBaudRate = m_sConnStr2.toInt();
    DebugLog(QString("调用SetCom%1, %2").arg(nComId).arg(nBaudRate));
    bool bRet = WtSys_SetCom(m_sConnStr1.toLatin1().data(), nBaudRate);
    if (!bRet) {
        ErrorLog(QObject::tr("打开轴重仪失败，com:%1，baud:%2").arg(nComId).arg(nBaudRate));
        ChangeStatus(DEV_STATUS_CommErr);
        return false;
    } else {
        InfoLog(QObject::tr("打开轴重仪成功，com:%1，baud:%2").arg(nComId).arg(nBaudRate));
    }

    WtSys_Init(0);

    m_bDevInited = true;

    VehWeightInfo::GetVehWeightInfo()->LoadFromFile();

    m_WorkThread.SetWtDev(this);
    m_WorkThread.ResumeThread();
    ErrorLog("计重StartDev结束");
    ChangeStatus(DEV_STATUS_OK);
    return true;
}

void CWtSysDev_ZC::CloseDev()
{
    m_WorkThread.StopThread();
    if (m_bDriverLoaded && m_bDevInited) {
        WtSys_CloseCom();
        m_bDevInited = false;
    }
}

bool CWtSysDev_ZC::LoadDriver()
{
    if (m_bDriverLoaded) {
        return true;
    }
    QString sPath = QDir::currentPath()+"/"+ m_sDriver;
    m_hLibModule.setFileName(sPath);

    if (!m_hLibModule.load()) {
        m_lastError = QString("加载动态库%1失败").arg(sPath);
        ErrorLog(m_lastError);
        ChangeStatus(DEV_STATUS_LibErr);
        return false;
    }

    // 加载各个函数
    QString sMsg;
    
    // 1. 整车式称重系统缓存初始化
    WtSys_Init = (Func_WtSys_Init)LoadFunc(m_hLibModule, "WtSys_Init");
    if (!WtSys_Init) sMsg = QString("WtSys_Init");
    
    // 2. 检查称重系统设备状态
    WtSys_Test = (Func_WtSys_Test)LoadFunc(m_hLibModule, "WtSys_Test");
    if (!WtSys_Test) {
        sMsg += QString(",WtSys_Test");
    }
    
    // 3. 设置串口
    WtSys_SetCom = (Func_WtSys_SetCom)LoadFunc(m_hLibModule, "WtSys_SetCom");
    if (!WtSys_SetCom) {
        sMsg += QString(",WtSys_SetCom");
    }
    
    // 4. 关闭串口，释放动态链接库占用系统资源
    WtSys_CloseCom = (Func_WtSys_CloseCom)LoadFunc(m_hLibModule, "WtSys_CloseCom");
    if (!WtSys_CloseCom) {
        sMsg += QString(",WtSys_CloseCom");
    }
    
    // 5. 清除保存的首辆车数据
    WtSys_ClearOne = (Func_WtSys_ClearOne)LoadFunc(m_hLibModule, "WtSys_ClearOne");
    if (!WtSys_ClearOne) {
        sMsg += QString(",WtSys_ClearOne");
    }
    
    // 6. 取当前总车数
    WtSys_GetVehicleCount = (Func_WtSys_GetVehicleCount)LoadFunc(m_hLibModule, "WtSys_GetVehicleCount");
    if (!WtSys_GetVehicleCount) {
        sMsg += QString(",WtSys_GetVehicleCount");
    }
    
    // 7. 取指定序号的车辆总轴组数
    WtSys_GetAxisCount = (Func_WtSys_GetAxisCount)LoadFunc(m_hLibModule, "WtSys_GetAxisCount");
    if (!WtSys_GetAxisCount) {
        sMsg += QString(",WtSys_GetAxisCount");
    }
    
    // 8. 取指定轴组数据
    WtSys_GetAxisData = (Func_WtSys_GetAxisData)LoadFunc(m_hLibModule, "WtSys_GetAxisData");
    if (!WtSys_GetAxisData) {
        sMsg += QString(",WtSys_GetAxisData");
    }
    
    // 9. 模拟手动收尾，用于超长车分段称量
    WtSys_ManualFinishing = (Func_WtSys_ManualFinishing)LoadFunc(m_hLibModule, "WtSys_ManualFinishing");
    
    // 10. 控制整车式称重系统前辅助分车栏杆抬起
    WtSys_OpenManual = (Func_WtSys_OpenManual)LoadFunc(m_hLibModule, "WtSys_OpenManual");
    if (!WtSys_OpenManual) {
        sMsg += QString(",WtSys_OpenManual");
    }
    
    // 11. 控制整车式称重系统前辅助分车栏杆落下
    WtSys_CloseManual = (Func_WtSys_CloseManual)LoadFunc(m_hLibModule, "WtSys_CloseManual");
    if (!WtSys_CloseManual) {
        sMsg += QString(",WtSys_CloseManual");
    }
    
    // 12. 检测是否有多辆车在称重平台上
    WtSys_IsVehicleAlone = (Func_WtSys_IsVehicleAlone)LoadFunc(m_hLibModule, "Wtsys_IsVehicleAlone");
    if (!WtSys_IsVehicleAlone) {
        // 尝试大写版本
        WtSys_IsVehicleAlone = (Func_WtSys_IsVehicleAlone)LoadFunc(m_hLibModule, "WtSys_IsVehicleAlone");
        if (!WtSys_IsVehicleAlone) {
            sMsg += QString(",WtSys_IsVehicleAlone");
        }
    }
    
    // 13. 手动重新称重
    WtSys_UploadVehicleAgain = (Func_WtSys_UploadVehicleAgain)LoadFunc(m_hLibModule, "WtSys_UpLoadVehicleAgain");
    if (!WtSys_UploadVehicleAgain) {
        sMsg += QString(",WtSys_UploadVehicleAgain");
    }
    
    // 14. 获取整车式称重系统参数
    WtSys_GetDevParameter = (Func_WtSys_GetDevParameter)LoadFunc(m_hLibModule, "WtSys_GetDevParameter");
    
    // 15. 获取整车式称重系统制造商参数
    WtSys_GetFactoryInfo = (Func_WtSys_GetFactoryInfo)LoadFunc(m_hLibModule, "WtSys_GetFactoryInfo");
    
    // 加载DevFinish函数
    m_wtSysDevFinish = (Func_WtSys_DevFinish)LoadFunc(m_hLibModule, "WtSys_DevFinish");
    if (m_wtSysDevFinish) {
        InfoLog("WtSys_DevFinish函数加载成功");
        m_hasDevFinish = true;
    } else {
        DebugLog("WtSys_DevFinish函数未找到");
    }

    if (sMsg.length() > 0) {
        DebugLog(QString("计重动态库缺少函数:%1").arg(sMsg));
    }

    if (NULL == WtSys_Init || NULL == WtSys_Test || NULL == WtSys_SetCom ||
        NULL == WtSys_CloseCom || NULL == WtSys_ClearOne || NULL == WtSys_GetVehicleCount ||
        NULL == WtSys_GetAxisCount || NULL == WtSys_GetAxisData || 
        NULL == WtSys_OpenManual || NULL == WtSys_CloseManual) {
        ErrorLog(QString("获取计重动态库[%1]中一个或多个函数失败").arg(sPath));
        ReleaseDriver();
        ChangeStatus(DEV_STATUS_LibErr);
        return false;
    }

    m_bDriverLoaded = true;
    ErrorLog("计重库加载成功");
    return true;
}

void CWtSysDev_ZC::ReleaseDriver()
{
    // 将动态库置为未加载
    m_bDriverLoaded = false;

    // 重置所有函数指针
    WtSys_Init = NULL;
    WtSys_Test = NULL;
    WtSys_SetCom = NULL;
    WtSys_CloseCom = NULL;
    WtSys_ClearOne = NULL;
    WtSys_GetVehicleCount = NULL;
    WtSys_GetAxisCount = NULL;
    WtSys_GetAxisData = NULL;
    WtSys_ManualFinishing = NULL;
    WtSys_OpenManual = NULL;
    WtSys_CloseManual = NULL;
    WtSys_IsVehicleAlone = NULL;
    WtSys_UploadVehicleAgain = NULL;
    WtSys_GetDevParameter = NULL;
    WtSys_GetFactoryInfo = NULL;
    m_wtSysDevFinish = NULL;
    m_hasDevFinish = false;

    m_hLibModule.unload();
}

void CWtSysDev_ZC::GetWeightData()
{
    int nVehCount = WtSys_GetVehicleCount();

    if (nVehCount != m_LastCount) {
        DebugLog(QString("获取车辆数:%1,%2").arg(nVehCount).arg(m_LastCount));
        m_LastCount = nVehCount;
    }
    for (int i = 1; i <= nVehCount; i++) {
        CVehAxisInfo VehAxisInfo;
        int nAxisCount = WtSys_GetAxisCount(1);
        DebugLog(QObject::tr("原始轴重数据：轴组数%1").arg(nAxisCount));
        for (int j = 1; j <= nAxisCount; j++) {
            CAxisData AxisData;
            int nAxisType = 0;
            long lAxisWeight = 0;
            int lSpeed = 0;
            // 计量版本号
            int MeterVer;
            WtSys_GetAxisData(1, j, &nAxisType, &lAxisWeight, &lSpeed, &MeterVer);
            AxisData.m_nAxisType = nAxisType;
            AxisData.m_nAxisWeight = lAxisWeight;
            AxisData.m_nSpeed = lSpeed;
            DebugLog(QObject::tr("原始轴重数据：第%1轴组 %2型 %3公斤 %4")
                         .arg(j)
                         .arg(AxisData.m_nAxisType)
                         .arg(AxisData.m_nAxisWeight)
                         .arg(AxisData.m_nSpeed));
            VehAxisInfo.AddRawAxis(AxisData.m_nAxisType, AxisData.m_nAxisWeight, AxisData.m_nSpeed);
        }
        if (!VehAxisInfo.IsNullVeh()) {
            // 获取车辆外轮廓尺寸信息
            GetVehicleOutlineInfo(VehAxisInfo);

            VehWeightInfo::GetVehWeightInfo()->AddVeh(VehAxisInfo);
        }
        WtSys_ClearOne();
    }

    static int c = 1;
    if ((c++) > 5) {
        c = 1;
        int nStatus = WtSys_Test();
        if (nStatus != m_nWtStatus) {
            DebugLog(QString("称重设备返回状态:%1").arg(nStatus));
            m_nWtStatus = nStatus;
        }
        if (0 != nStatus)
            ChangeStatus(DEV_STATUS_CommErr);
        else {
            ChangeStatus(DEV_STATUS_OK);
        }
    }
}

bool CWtSysDev_ZC::UploadVehWeightAgain()
{
    // 修改为调用DevFinish_HD函数
    InfoLog("开始执行人工称重功能，调用DevFinish_HD");
    return DevFinish_HD();
}

bool CWtSysDev_ZC::DevFinish_HD()
{
    bool result = false;
    
    if (m_hasDevFinish && m_wtSysDevFinish) {
        // 使用WtSys_DevFinish接口
        InfoLog("开始执行DevFinish函数");
        int retCode = m_wtSysDevFinish();
        
        if (retCode >= 0) {
            InfoLog(QString("DevFinish函数执行成功，返回值: %1").arg(retCode));
            result = true;
        } else {
            ErrorLog(QString("DevFinish函数执行失败，错误码: %1").arg(retCode));
            // 失败时使用备用方案
            InfoLog("DevFinish失败，使用备用方案");
            result = UseEntryWeightInfo(NULL);
        }
    } else {
        // 如果没有WtSys_DevFinish接口，则使用入口称重信息方案
        InfoLog("DevFinish函数未加载，使用入口称重信息方案");
        result = UseEntryWeightInfo(NULL);
    }
    
    return result;
}

bool CWtSysDev_ZC::bUploadWeightAgain() 
{ 
    return m_hasDevFinish || WtSys_UploadVehicleAgain != NULL; 
}

bool CWtSysDev_ZC::UseEntryWeightInfo(QString* pError)
{
    if (!pError) {
        pError = new QString();
    }
    
    InfoLog("开始使用入口称重信息");
#ifndef WEIGHTTESTER
    // 获取当前交易信息
    CTransInfo *pTransInfo = Ptr_ETCCtrl->GetCurTransInfo(DevIndex_Manual);
    if (!pTransInfo) {
        *pError = "获取交易信息失败";
        ErrorLog(*pError);
        return false;
    }
    
    // 获取入口称重信息
    CVehEntryInfo &vehEntryInfo = pTransInfo->vehEntryInfo;
    quint32 dwTotalWeight = vehEntryInfo.dwTotalWeight;
    quint32 dwWeightLimit = vehEntryInfo.dwWeightLimit;
    int nAxisNum = vehEntryInfo.VehicalAxles;
    
    InfoLog(QString("获取到入口称重数据: 总重=%1kg, 限重=%2kg, 轴数=%3")
            .arg(dwTotalWeight)
            .arg(dwWeightLimit)
            .arg(nAxisNum));
    
    // 检查称重数据有效性
    if (!ValidateWeightData(dwTotalWeight, nAxisNum, pError)) {
        return false;
    }
    
    // 创建新的轴重信息对象
    CVehAxisInfo vehAxisInfo;
    
    // 根据轴数分配轴组和重量
    bool bSuccess = DistributeWeightToAxleGroups(vehAxisInfo, dwTotalWeight, nAxisNum, pError);
    if (!bSuccess) {
        return false;
    }
    
    // 设置限载重量（如果有）
    if (dwWeightLimit > 0) {
        vehAxisInfo.SetVehLimitWeight(dwWeightLimit);
        InfoLog(QString("设置车辆限重为: %1kg").arg(dwWeightLimit));
    }
    
    // 添加到称重队列
    VehWeightInfo *pVehWeightInfo = VehWeightInfo::GetVehWeightInfo();
    if (!pVehWeightInfo) {
        *pError = "获取VehWeightInfo实例失败";
        ErrorLog(*pError);
        return false;
    }
    
    // 保存原始称重数据，用于可能的回退操作
    SaveOriginalWeightData(pVehWeightInfo);
    
    // 添加新的称重数据
    pVehWeightInfo->AddVeh(vehAxisInfo);
    InfoLog(QString("已将入口称重数据添加到称重队列"));
#endif
    return true;
}

bool CWtSysDev_ZC::ValidateWeightData(quint32 dwTotalWeight, int nAxisNum, QString* pError)
{
    // 验证总重
    if (dwTotalWeight == 0) {
        *pError = "入口称重数据无效: 总重为0";
        ErrorLog(*pError);
        return false;
    }
    
    if (dwTotalWeight >= 0x00ffffff) {
        *pError = QString("入口称重数据无效: 总重超出合理范围(%1kg)").arg(dwTotalWeight);
        ErrorLog(*pError);
        return false;
    }
    
    // 验证轴数
    if (nAxisNum <= 0 || nAxisNum > 17) {
        *pError = QString("入口称重数据无效: 轴数异常(%1)").arg(nAxisNum);
        ErrorLog(*pError);
        return false;
    }
    
    return true;
}

bool CWtSysDev_ZC::DistributeWeightToAxleGroups(CVehAxisInfo &vehAxisInfo, quint32 dwTotalWeight, int nAxisNum, QString* pError)
{
    InfoLog(QString("开始按轴数(%1)分配总重(%2kg)").arg(nAxisNum).arg(dwTotalWeight));
    
    try {
        // 使用double类型进行计算，避免整除精度损失
        double totalWeight = static_cast<double>(dwTotalWeight);
        
        switch (nAxisNum) {
            case 2: {
                // 轴数等于2时，轴型分成2组，每组类型均为1，总重平分
                double weightPerGroup = totalWeight / 2.0;
                quint32 firstGroupWeight = static_cast<quint32>(weightPerGroup);
                quint32 secondGroupWeight = dwTotalWeight - firstGroupWeight; // 确保总和正确
                
                vehAxisInfo.AddRawAxis(1, firstGroupWeight, 0);  // 第一组，类型1
                vehAxisInfo.AddRawAxis(1, secondGroupWeight, 0);  // 第二组，类型1
                
                InfoLog(QString("2轴车辆: 第一组(类型1)=%1kg, 第二组(类型1)=%2kg")
                        .arg(firstGroupWeight).arg(secondGroupWeight));
                break;
            }
            case 3: {
                // 轴数等于3时，轴型分为2组，第一组类型为1，第二组类型为5，总重三分，第一组得一分，第二组得2分
                double weightUnit = totalWeight / 3.0;
                quint32 firstGroupWeight = static_cast<quint32>(weightUnit);
                quint32 secondGroupWeight = dwTotalWeight - firstGroupWeight; // 确保总和正确
                
                vehAxisInfo.AddRawAxis(1, firstGroupWeight, 0);  // 第一组，类型1
                vehAxisInfo.AddRawAxis(5, secondGroupWeight, 0);  // 第二组，类型5
                
                InfoLog(QString("3轴车辆: 第一组(类型1)=%1kg, 第二组(类型5)=%2kg")
                        .arg(firstGroupWeight).arg(secondGroupWeight));
                break;
            }
            case 4: {
                // 轴数等于4时，轴型分为3组，第一组类型为1，第二组类型为2，第三组类型为5
                double weightUnit = totalWeight / 4.0;
                quint32 firstGroupWeight = static_cast<quint32>(weightUnit);
                quint32 secondGroupWeight = static_cast<quint32>(weightUnit);
                quint32 thirdGroupWeight = dwTotalWeight - firstGroupWeight - secondGroupWeight; // 确保总和正确
                
                vehAxisInfo.AddRawAxis(1, firstGroupWeight, 0);  // 第一组，类型1
                vehAxisInfo.AddRawAxis(2, secondGroupWeight, 0);  // 第二组，类型2
                vehAxisInfo.AddRawAxis(5, thirdGroupWeight, 0);  // 第三组，类型5
                
                InfoLog(QString("4轴车辆: 第一组(类型1)=%1kg, 第二组(类型2)=%2kg, 第三组(类型5)=%3kg")
                        .arg(firstGroupWeight).arg(secondGroupWeight).arg(thirdGroupWeight));
                break;
            }
            case 5: {
                // 轴数等于5时，轴型分为3组，第一组类型为1，第二组类型为5，第三组类型为5
                double weightUnit = totalWeight / 5.0;
                quint32 firstGroupWeight = static_cast<quint32>(weightUnit);
                quint32 secondGroupWeight = static_cast<quint32>(weightUnit * 2.0);
                quint32 thirdGroupWeight = dwTotalWeight - firstGroupWeight - secondGroupWeight; // 确保总和正确
                
                vehAxisInfo.AddRawAxis(1, firstGroupWeight, 0);  // 第一组，类型1
                vehAxisInfo.AddRawAxis(5, secondGroupWeight, 0);  // 第二组，类型5
                vehAxisInfo.AddRawAxis(5, thirdGroupWeight, 0);  // 第三组，类型5
                
                InfoLog(QString("5轴车辆: 第一组(类型1)=%1kg, 第二组(类型5)=%2kg, 第三组(类型5)=%3kg")
                        .arg(firstGroupWeight).arg(secondGroupWeight).arg(thirdGroupWeight));
                break;
            }
            case 6: {
                // 轴数等于6时，轴型分为3组，第一组类型为1，第二组类型为5，第三组类型为7
                double weightUnit = totalWeight / 6.0;
                quint32 firstGroupWeight = static_cast<quint32>(weightUnit);
                quint32 secondGroupWeight = static_cast<quint32>(weightUnit * 2.0);
                quint32 thirdGroupWeight = dwTotalWeight - firstGroupWeight - secondGroupWeight; // 确保总和正确
                
                vehAxisInfo.AddRawAxis(1, firstGroupWeight, 0);  // 第一组，类型1
                vehAxisInfo.AddRawAxis(5, secondGroupWeight, 0);  // 第二组，类型5
                vehAxisInfo.AddRawAxis(7, thirdGroupWeight, 0);  // 第三组，类型7
                
                InfoLog(QString("6轴车辆: 第一组(类型1)=%1kg, 第二组(类型5)=%2kg, 第三组(类型7)=%3kg")
                        .arg(firstGroupWeight).arg(secondGroupWeight).arg(thirdGroupWeight));
                break;
            }
            default: {
                // 默认情况，轴数超过6或小于2时，只添加一个轴组
                vehAxisInfo.AddRawAxis(1, dwTotalWeight, 0);
                InfoLog(QString("其他轴数(%1)车辆: 单轴组(类型1)=%2kg").arg(nAxisNum).arg(dwTotalWeight));
                break;
            }
        }
        
        // 验证分配的重量总和是否等于原始总重量
        QList<CAxisData> axleList;
        vehAxisInfo.GetRawAxisList(&axleList);
        quint32 sumWeight = 0;
        for (int i = 0; i < axleList.size(); i++) {
            sumWeight += axleList[i].m_nAxisWeight;
        }
        
        if (sumWeight != dwTotalWeight) {
            WarnLog(QString("警告：分配的轴组重量总和(%1kg)与原始总重(%2kg)不一致，差值为%3kg")
                    .arg(sumWeight).arg(dwTotalWeight).arg(dwTotalWeight - sumWeight));
            // 校正最后一个轴组的重量以确保总和正确
            if (!axleList.isEmpty()) {
                quint32 lastAxleWeight = axleList.back().m_nAxisWeight;
                quint32 correctedWeight = lastAxleWeight + (dwTotalWeight - sumWeight);
                
                // 重新设置轴组数据
                axleList.clear();
                vehAxisInfo.GetRawAxisList(&axleList);
                axleList.back().m_nAxisWeight = correctedWeight;
                
                InfoLog(QString("已校正最后轴组重量：%1kg -> %2kg")
                        .arg(lastAxleWeight).arg(correctedWeight));
            }
        }
        
        return true;
    } catch (...) {
        *pError = "分配轴组重量时发生未知异常";
        ErrorLog(*pError);
        return false;
    }
}

void CWtSysDev_ZC::SaveOriginalWeightData(VehWeightInfo* pVehWeightInfo)
{
    if (!pVehWeightInfo) {
        ErrorLog("保存原始称重数据失败：VehWeightInfo实例为空");
        return;
    }
    
    // 创建备份文件路径
    QString backupPath = "./AxisInfo_Backup.sav";
    
    // 获取当前所有车辆信息
    QList<CVehAxisInfo> vehList;
    pVehWeightInfo->GetAllVeh(&vehList);
    
    // 将当前车辆信息保存到备份文件
    QFile fFile;
    fFile.setFileName(backupPath);
    if (!fFile.open(QIODevice::WriteOnly)) {
        ErrorLog(QString("无法创建称重数据备份文件: %1").arg(backupPath));
        return;
    }
    
    try {
        // 写入车辆数量
        int nVehCount = vehList.size();
        fFile.write((char*)&nVehCount, sizeof(int));
        
        // 写入每辆车的轴组信息
        for (int i = 0; i < vehList.size(); i++) {
            QList<CAxisData> axisList;
            vehList[i].GetConfirmedAxisList(&axisList);
            
            int nAxisCount = axisList.size();
            fFile.write((char*)&nAxisCount, sizeof(int));
            
            for (int j = 0; j < axisList.size(); j++) {
                fFile.write((char*)&axisList[j], sizeof(CAxisData));
            }
        }
        
        fFile.flush();
        InfoLog(QString("已保存原始称重数据至备份文件: %1").arg(backupPath));
    } catch (...) {
        ErrorLog("保存原始称重数据时发生异常");
    }
    
    fFile.close();
}

bool CWtSysDev_ZC::RestoreWeightData()
{
    InfoLog("开始从备份恢复称重数据");
    
    // 获取VehWeightInfo实例
    VehWeightInfo* pVehWeightInfo = VehWeightInfo::GetVehWeightInfo();
    if (!pVehWeightInfo) {
        ErrorLog("恢复称重数据失败：无法获取VehWeightInfo实例");
        return false;
    }
    
    // 备份文件路径
    QString backupPath = "./AxisInfo_Backup.sav";
    
    // 检查备份文件是否存在
    if (!QFile::exists(backupPath)) {
        ErrorLog(QString("恢复称重数据失败：备份文件不存在: %1").arg(backupPath));
        return false;
    }
    
    // 清空当前称重队列
    pVehWeightInfo->RemoveAll();
    
    // 从备份文件加载数据
    QFile fFile;
    fFile.setFileName(backupPath);
    if (!fFile.open(QIODevice::ReadOnly)) {
        ErrorLog(QString("恢复称重数据失败：无法打开备份文件: %1").arg(backupPath));
        return false;
    }
    
    try {
        // 读取车辆数量
        int nVehCount = 0;
        fFile.read((char*)&nVehCount, sizeof(int));
        
        // 读取每辆车的轴组信息并添加到队列
        for (int i = 0; i < nVehCount; i++) {
            CVehAxisInfo vehInfo;
            int nAxisCount = 0;
            
            fFile.read((char*)&nAxisCount, sizeof(int));
            
            for (int j = 0; j < nAxisCount; j++) {
                CAxisData axisData;
                fFile.read((char*)&axisData, sizeof(CAxisData));
                
                // 检查数据有效性
                if (axisData.m_nAxisType > 9) {
                    ErrorLog("恢复称重数据失败：读取到无效的轴型数据");
                    throw;
                }
                
                vehInfo.AddRawAxis(axisData.m_nAxisType, axisData.m_nAxisWeight, axisData.m_nSpeed);
            }
            
            // 添加车辆到称重队列
            pVehWeightInfo->AddVeh(vehInfo);
        }
        
        InfoLog(QString("成功从备份文件恢复了%1辆车的称重数据").arg(nVehCount));
    } catch (...) {
        ErrorLog("恢复称重数据时发生未知异常");
        fFile.close();
        return false;
    }
    
    fFile.close();
    return true;
}

void CWtSysDev_ZC::NotifyWeightError(const QString& sError)
{
    // 发送错误信号
    ErrorLog(QString("称重错误通知: %1").arg(sError));
    emit WeightError(sError);
}

void CWtSysDev_ZC::GetVehicleOutlineInfo(CVehAxisInfo& vehAxisInfo)
{
    try {
        // 获取车辆外轮廓尺寸检测仪设备实例
        CVehicleOutlineDev* pVehicleOutlineDev = CDeviceFactory::GetVehicleOutlineDev();
        if (!pVehicleOutlineDev) {
            DebugLog("获取车辆外轮廓尺寸检测仪设备实例失败");
            return;
        }

        // 检查设备状态
        if (!pVehicleOutlineDev->CheckDeviceStatus()) {
            DebugLog("车辆外轮廓尺寸检测仪设备状态异常");
            return;
        }

        // 获取最新的车辆外轮廓数据
        VehicleOutlineData outlineData;
        if (pVehicleOutlineDev->GetLatestVehicleOutlineInfo(outlineData)) {
            if (outlineData.isValid()) {
                // 设备返回的单位是分米，需要转换为毫米保存
                vehAxisInfo.SetVehOutlineFromDecimeter(outlineData.length,
                                                      outlineData.width,
                                                      outlineData.height);

                DebugLog(QString("获取车辆外轮廓信息成功 - 长:%1分米, 宽:%2分米, 高:%3分米 (转换为毫米: %4x%5x%6)")
                         .arg(outlineData.length)
                         .arg(outlineData.width)
                         .arg(outlineData.height)
                         .arg(vehAxisInfo.GetVehLength())
                         .arg(vehAxisInfo.GetVehWidth())
                         .arg(vehAxisInfo.GetVehHeight()));

                // 获取数据后删除当前信息
                pVehicleOutlineDev->DeleteVehicleOutlineInfo(0);
                DebugLog("已删除车辆外轮廓检测数据");
            } else {
                DebugLog("获取到的车辆外轮廓数据无效");
            }
        } else {
            DebugLog("获取车辆外轮廓信息失败或队列为空");
        }
    } catch (...) {
        ErrorLog("获取车辆外轮廓信息时发生异常");
    }
}

bool CWtSysThread_ZC::RunOnce()
{
    if (m_pWtDev->GetWimDriverLoaded()) {
        m_pWtDev->GetWeightData();
        CheckExit(600);
    } else {
        CheckExit(2000);
    }
    return true;
}

/**
 * @brief 设置驱动文件名
 * @param sDriver 驱动文件名
 */
void CWtSysDev_ZC::SetDriver(const QString& sDriver)
{
    m_sDriver = sDriver;
}

/**
 * @brief 设置连接字符串1
 * @param sConnStr1 串口号等信息
 */
void CWtSysDev_ZC::SetConnStr1(const QString& sConnStr1)
{
    m_sConnStr1 = sConnStr1;
}

/**
 * @brief 设置连接字符串2
 * @param sConnStr2 波特率等信息
 */
void CWtSysDev_ZC::SetConnStr2(const QString& sConnStr2)
{
    m_sConnStr2 = sConnStr2;
}

