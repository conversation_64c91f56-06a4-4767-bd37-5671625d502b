#include "FormProvinceRepayInfo.h"
#include <QPainter>
#include <QKeyEvent>
#include <QDateTime>
#include <QScrollBar>
#include "../../globalui.h"
#include "../../dlgmain.h"
#include "../../log4qt/ilogmsg.h"

FormProvinceRepayInfo::FormProvinceRepayInfo(QWidget *parent)
    : CBaseOpWidget(parent)
    , m_pLblTitle(0)
    , m_pLblTableHeader1(0)
    , m_pLblTableHeader2(0)
    , m_pLblTableHeader3(0)
    , m_pScrollArea(0)
    , m_pTableContent(0)
    , m_pLblReasonTitle(0)
    , m_pLblReasonContent(0)
    , m_pLblHelpInfo(0)
    , m_vehPlateColor(0)
    , m_selectedIndex(0)
    , m_totalAmount(0)
    , m_bDataLoaded(false)
{
    CreateControls();
    SetupControlProperties();
    InitConnections();
    
    filterChildrenKeyEvent();
    setObjectName("FormProvinceRepayInfo");
}

FormProvinceRepayInfo::~FormProvinceRepayInfo()
{
    // 不再使用异步查询，无需断开或停止定时器
    // Qt会自动清理子控件
}

void FormProvinceRepayInfo::CreateControls()
{
    // 创建界面控件
    m_pLblTitle = new QLabel(this);
    
    // 表格标题
    m_pLblTableHeader1 = new QLabel(QString::fromUtf8("出口收费站"), this);
    m_pLblTableHeader2 = new QLabel(QString::fromUtf8("时间"), this);
    m_pLblTableHeader3 = new QLabel(QString::fromUtf8("补费金额"), this);
    
    // 滚动区域和表格内容
    m_pScrollArea = new QScrollArea(this);
    m_pTableContent = new QWidget();
    m_pScrollArea->setWidget(m_pTableContent);
    m_pScrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_pScrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    
    // 原因描述
    m_pLblReasonTitle = new QLabel(QString::fromUtf8("【原因描述】"), this);
    m_pLblReasonContent = new QLabel(QString::fromUtf8("显示上面选中项的原因描述段的内容"), this);
    
    // 帮助信息
    m_pLblHelpInfo = new QLabel(QString::fromUtf8("按【↑】【↓】键查看  按【确认】键继续"), this);
}

void FormProvinceRepayInfo::SetupControlProperties()
{
    // 设置字体
    m_fontTitle = QFont(g_GlobalUI.m_FontName, 18);
    m_fontHeader = QFont(g_GlobalUI.m_FontName, 16);
    // 表格内容字体再缩小一号
    m_fontContent = QFont(g_GlobalUI.m_FontName, 12);
    m_fontHelp = QFont(g_GlobalUI.m_FontName, 12);
    
    // 设置背景色
    m_colorBackground = g_GlobalUI.m_ColorBackground;
    m_colorSelected = QColor(0, 180, 255);  // 蓝色选中背景
    
    // 标题
    m_pLblTitle->setFont(m_fontTitle);
    m_pLblTitle->setAlignment(Qt::AlignCenter);
    
    // 表格标题
    m_pLblTableHeader1->setFont(m_fontHeader);
    m_pLblTableHeader1->setAlignment(Qt::AlignCenter);
    m_pLblTableHeader1->setStyleSheet("border: 1px solid black; background-color: lightgray; padding: 5px;");
    
    m_pLblTableHeader2->setFont(m_fontHeader);
    m_pLblTableHeader2->setAlignment(Qt::AlignCenter);
    m_pLblTableHeader2->setStyleSheet("border: 1px solid black; background-color: lightgray; padding: 5px;");
    
    m_pLblTableHeader3->setFont(m_fontHeader);
    m_pLblTableHeader3->setAlignment(Qt::AlignCenter);
    m_pLblTableHeader3->setStyleSheet("border: 1px solid black; background-color: lightgray; padding: 5px;");
    
    // 滚动区域
    m_pScrollArea->setStyleSheet("border: 1px solid black;");
    
    // 原因描述
    m_pLblReasonTitle->setFont(m_fontContent);
    m_pLblReasonTitle->setAlignment(Qt::AlignLeft);
    m_pLblReasonTitle->setStyleSheet("border: 1px solid black; padding: 5px; background-color: white;");
    
    m_pLblReasonContent->setFont(m_fontContent);
    m_pLblReasonContent->setAlignment(Qt::AlignLeft);
    m_pLblReasonContent->setWordWrap(false);
    m_pLblReasonContent->setStyleSheet("border: 1px solid black; padding: 5px; background-color: white;");
    
    // 帮助信息
    m_pLblHelpInfo->setFont(m_fontHelp);
    m_pLblHelpInfo->setAlignment(Qt::AlignCenter);
    m_pLblHelpInfo->setStyleSheet("color: rgb(100, 100, 100);");
}

void FormProvinceRepayInfo::InitLayout()
{
    int width = rect().width();
    int height = rect().height();
    
    // 标题
    m_pLblTitle->setGeometry(20, 20, width - 40, 40);
    
    // 表格标题
    int tableY = 80;
    int headerHeight = 35;
    // 表格整体加宽
    int col1Width = 200;   // 出口收费站列宽
    int col2Width = 260;   // 时间列宽
    int col3Width = 130;   // 补费金额列宽
    int tableX = (width - (col1Width + col2Width + col3Width)) / 2;
    
    m_pLblTableHeader1->setGeometry(tableX, tableY, col1Width, headerHeight);
    m_pLblTableHeader2->setGeometry(tableX + col1Width, tableY, col2Width, headerHeight);
    m_pLblTableHeader3->setGeometry(tableX + col1Width + col2Width, tableY, col3Width, headerHeight);
    
    // 表格内容区域
    int tableContentY = tableY + headerHeight;
    // 表格高度减小，给原因描述预留空间
    int tableContentHeight = 125;  // 再降低一行高度空间
    int tableWidth = col1Width + col2Width + col3Width;
    m_pScrollArea->setGeometry(tableX, tableContentY, tableWidth, tableContentHeight);
    
    // 原因描述
    // 原因描述紧跟表格，宽度与表格相同，底色与表格内容一致
    int reasonY = tableContentY + tableContentHeight + 8;
    if (m_pLblReasonTitle) m_pLblReasonTitle->hide(); // 去掉标题行
    m_pLblReasonContent->setGeometry(tableX, reasonY, tableWidth, 56);
    
    // 帮助信息：避免与原因描述重叠
    int helpY = reasonY + 56 + 12; // 内容56 + 间距12
    if (helpY + 30 > height) helpY = height - 50;
    m_pLblHelpInfo->setGeometry(20, helpY, width - 40, 30);
}

void FormProvinceRepayInfo::InitConnections()
{
    // 不再使用异步查询，省略信号与定时器
}

bool FormProvinceRepayInfo::ShowRepayInfo(const QString &vehPlate, int vehPlateColor, const RepayDebtQueryResult &result)
{
    InfoLog(QString("开始显示省内名单补费信息界面 - 车牌:%1").arg(vehPlate));
    
    // 保存参数
    m_vehPlate = vehPlate;
    m_vehPlateColor = vehPlateColor;
    m_selectedIndex = 0;
    m_totalAmount = 0;
    m_bDataLoaded = false;
    
    // 初始化界面
    InitUI();
    InitLayout();
    
    // 设置标题（直接显示结果）
    QString vehColorName = GetVehPlateColorName(vehPlateColor);
    // 去掉“当前车辆”字样，仅显示“【蓝赣T00099】补费总金额：xxx元”
    m_pLblTitle->setText(QString::fromUtf8("【%1%2】补费总金额：%3元")
                        .arg(vehColorName).arg(vehPlate)
                        .arg(FormatAmount(result.totalAmount)));

    // 直接加载传入的数据并刷新表格
    m_debtResult = result;
    m_totalAmount = result.totalAmount;
    m_bDataLoaded = true;

    UpdateDebtTable();
    UpdateSelectedRow();
    UpdateReasonDescription();

    // 显示模态对话框
    return (CBaseOpWidget::Rlt_OK == doModalShow());
}

void FormProvinceRepayInfo::InitUI()
{
    CBaseOpWidget::InitUI();
}

int FormProvinceRepayInfo::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    if (!mtcKeyEvent) return 0;

    mtcKeyEvent->setKeyType(KC_Func);
    
    switch (mtcKeyEvent->func()) {
        case KeyConfirm:
            OnConfirmClicked();
            break;
            
        case KeyEsc:
            OnCancelClicked();
            break;
            
        case KeyUp:
            // 上一行
            if (m_selectedIndex > 0) {
                m_selectedIndex--;
                UpdateSelectedRow();
                UpdateReasonDescription();
            }
            break;
            
        case KeyDown:
            // 下一行
            if (m_selectedIndex < m_debtResult.debtItems.size() - 1) {
                m_selectedIndex++;
                UpdateSelectedRow();
                UpdateReasonDescription();
            }
            break;
            
        default:
            break;
    }

    return 1;
}

void FormProvinceRepayInfo::UpdateDebtTable()
{
    // 清理旧的表格行
    foreach (QLabel *label, m_rowLabels1) {
        delete label;
    }
    foreach (QLabel *label, m_rowLabels2) {
        delete label;
    }
    foreach (QLabel *label, m_rowLabels3) {
        delete label;
    }
    m_rowLabels1.clear();
    m_rowLabels2.clear();
    m_rowLabels3.clear();
    
    // 创建新的表格行
    int rowHeight = 32;
    int col1Width = 200;
    int col2Width = 260;
    int col3Width = 130;
    
    for (int i = 0; i < m_debtResult.debtItems.size(); i++) {
        const RepayDebtItem &item = m_debtResult.debtItems[i];
        
        // 出口收费站
        QLabel *label1 = new QLabel(item.exitStation, m_pTableContent);
        label1->setFont(m_fontContent);
        label1->setAlignment(Qt::AlignCenter);
        label1->setStyleSheet("border: 1px solid black; padding: 5px;");
        label1->setGeometry(0, i * rowHeight, col1Width, rowHeight);
        m_rowLabels1.append(label1);
        
        // 时间
        QLabel *label2 = new QLabel(FormatDateTime(item.debtDate), m_pTableContent);
        label2->setFont(m_fontContent);
        label2->setAlignment(Qt::AlignCenter);
        label2->setStyleSheet("border: 1px solid black; padding: 5px;");
        label2->setGeometry(col1Width, i * rowHeight, col2Width, rowHeight);
        m_rowLabels2.append(label2);
        
        // 补费金额
        QLabel *label3 = new QLabel(FormatAmount(item.debtAmount), m_pTableContent);
        label3->setFont(m_fontContent);
        label3->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
        label3->setStyleSheet("border: 1px solid black; padding: 5px;");
        label3->setGeometry(col1Width + col2Width, i * rowHeight, col3Width, rowHeight);
        m_rowLabels3.append(label3);
    }
    
    // 设置表格内容的大小
    int totalHeight = m_debtResult.debtItems.size() * rowHeight;
    m_pTableContent->setFixedSize(col1Width + col2Width + col3Width, totalHeight);
}

void FormProvinceRepayInfo::UpdateSelectedRow()
{
    // 更新所有行的背景色
    for (int i = 0; i < m_rowLabels1.size(); i++) {
        QString bgColor = (i == m_selectedIndex) ? "background-color: lightblue;" : "background-color: white;";
        QString style = QString("border: 1px solid black; padding: 5px; %1").arg(bgColor);
        
        m_rowLabels1[i]->setStyleSheet(style);
        m_rowLabels2[i]->setStyleSheet(style);
        m_rowLabels3[i]->setStyleSheet(style);
    }
    
    // 滚动到选中行
    if (m_selectedIndex >= 0 && m_selectedIndex < m_rowLabels1.size()) {
        int rowHeight = 35;
        int scrollToY = m_selectedIndex * rowHeight;
        m_pScrollArea->verticalScrollBar()->setValue(scrollToY);
    }
}

void FormProvinceRepayInfo::UpdateReasonDescription()
{
    if (m_selectedIndex >= 0 && m_selectedIndex < m_debtResult.debtItems.size()) {
        const RepayDebtItem &item = m_debtResult.debtItems[m_selectedIndex];
        QString text = QString::fromUtf8("【原因描述】：%1").arg(item.remark.isEmpty() ? QString::fromUtf8("无") : item.remark);
        if (!item.passId.isEmpty()) {
            text += QString::fromUtf8("  通行ID：%1").arg(item.passId);
        }
        m_pLblReasonContent->setText(text);
    } else {
        m_pLblReasonContent->setText(QString::fromUtf8("【原因描述】："));
    }
}

void FormProvinceRepayInfo::OnConfirmClicked()
{
    if (!m_bDataLoaded) {
        InfoLog("数据尚未加载完成，无法确认");
        return;
    }
    
    // 已取消异步查询逻辑，无需等待
    
    if (m_debtResult.debtItems.isEmpty()) {
        InfoLog("无欠费记录，无法确认");
        return;
    }
    
    InfoLog(QString("用户确认省内名单补费信息 - 选中索引:%1, 总金额:%2分")
            .arg(m_selectedIndex).arg(m_totalAmount));
    OnOk();
}

void FormProvinceRepayInfo::OnCancelClicked()
{
    InfoLog("用户取消省内名单补费信息");
    OnCancel();
}

QString FormProvinceRepayInfo::GetVehPlateColorName(int color)
{
    switch (color) {
        case 0: return QString::fromUtf8("蓝");
        case 1: return QString::fromUtf8("黄");
        case 2: return QString::fromUtf8("黑");
        case 3: return QString::fromUtf8("白");
        case 4: return QString::fromUtf8("绿");
        default: return QString("");
    }
}

QString FormProvinceRepayInfo::FormatDateTime(const QString &datetime)
{
    // 格式化时间显示，如：2025-03-10 21:25:58
    return datetime;
}

QString FormProvinceRepayInfo::FormatAmount(int amount)
{
    // 将分转换为元，保留两位小数
    return QString::number(amount / 100.0, 'f', 2);
}

// 已移除异步查询相关槽函数

void FormProvinceRepayInfo::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event);
    
    QRect rectClient = this->rect();
    QPixmap pixmap(rectClient.width(), rectClient.height());
    QPainter painter(&pixmap);

    // 绘制背景
    painter.setBrush(m_colorBackground);
    painter.setPen(Qt::black);
    painter.drawRect(rectClient.x(), rectClient.y(), rectClient.width() - 1, rectClient.height() - 1);

    painter.end();
    QPainter ptThis(this);
    ptThis.drawPixmap(rectClient, pixmap);
}
