#include "FormDebtDetailSimple.h"
#include "../../dlgmain.h"
#include "../../log4qt/ilogmsg.h"
#include <QApplication>
#include <QKeyEvent>
#include <QDateTime>

// 静态常量定义
const int FormDebtDetailSimple::MAX_VISIBLE_ITEMS;

FormDebtDetailSimple::FormDebtDetailSimple(QWidget *parent)
    : CBaseOpWidget(parent)
    , m_nCurrentIndex(0)
    , m_nScrollOffset(0)
    , m_nTotalItems(0)
    , m_nTotalAmount(0)
    , m_pAutoCloseTimer(0)
{
    // 初始化定时器
    m_pAutoCloseTimer = new QTimer(this);
    connect(m_pAutoCloseTimer, SIGNAL(timeout()), this, SLOT(OnAutoCloseTimer()));
    
    // 初始化界面配置
    InitUIConfig();
    
    InfoLog("创建简化补费明细界面");
}

FormDebtDetailSimple::~FormDebtDetailSimple()
{
    InfoLog("销毁简化补费明细界面");
}

void FormDebtDetailSimple::InitUIConfig()
{
    // 设置字体
    m_fontTitle = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TitleFontSize, QFont::Bold);
    m_fontText = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TextFontSize);
    m_fontItem = QFont(g_GlobalUI.m_FontName, 12);
    m_fontSummary = QFont(g_GlobalUI.m_FontName, 14, QFont::Bold);
    
    // 设置颜色
    m_colorBackground = g_GlobalUI.m_ColorBackground;
    m_colorTitle = QColor(0, 0, 0);
    m_colorText = QColor(50, 50, 50);
    m_colorItem = QColor(248, 248, 248);
    m_colorSelectedItem = QColor(173, 216, 230);
    m_colorBorder = QColor(180, 180, 180);
    m_colorButton = QColor(0, 120, 215);
    m_colorSuccess = QColor(50, 150, 50);
    m_colorWarning = QColor(200, 120, 0);
}

void FormDebtDetailSimple::InitUI(int iFlag)
{
    CBaseOpWidget::InitUI(iFlag);
    
    InfoLog("初始化简化补费明细界面");
    
    // 设置窗口属性
    setWindowTitle(QString::fromUtf8("省内欠费明细"));
    
    DebugLog("简化补费明细界面初始化完成");
}

bool FormDebtDetailSimple::ShowDebtDetail(const RepayDebtQueryResult &result)
{
    InfoLog("显示简化补费明细界面");
    
    // 设置数据
    SetDebtResult(result);
    
    if (m_nTotalItems == 0) {
        ShowWarningMessage(QString::fromUtf8("未找到欠费明细"));
        return false;
    }
    
    // 启动自动关闭定时器
    m_pAutoCloseTimer->start(AUTO_CLOSE_TIMEOUT * 1000);
    
    // 显示界面
    int dialogResult = doModalShow();
    
    // 停止定时器
    m_pAutoCloseTimer->stop();
    
    if (dialogResult == CBaseOpWidget::Rlt_OK) {
        InfoLog(QString("明细确认完成 - 总金额:%1分").arg(m_nTotalAmount));
        return true;
    } else {
        InfoLog("明细查看取消");
        return false;
    }
}

int FormDebtDetailSimple::GetTotalRepayAmount() const
{
    return m_nTotalAmount;
}

void FormDebtDetailSimple::SetDebtResult(const RepayDebtQueryResult &result)
{
    m_debtResult = result;
    m_nTotalItems = result.debtItems.size();
    m_nCurrentIndex = 0;
    m_nScrollOffset = 0;
    
    // 更新汇总信息
    UpdateSummaryInfo();
    
    InfoLog(QString("设置债务结果 - 项目数:%1, 总金额:%2分")
            .arg(m_nTotalItems).arg(m_nTotalAmount));
}

void FormDebtDetailSimple::UpdateSummaryInfo()
{
    m_nTotalAmount = 0;
    
    // 计算总金额（使用所有债务项目的金额）
    for (int i = 0; i < m_debtResult.debtItems.size(); i++) {
        m_nTotalAmount += m_debtResult.debtItems[i].debtAmount;
    }
    
    // 如果没有明细项目，使用查询结果的总金额
    if (m_debtResult.debtItems.isEmpty() && m_debtResult.totalAmount > 0) {
        m_nTotalAmount = m_debtResult.totalAmount;
    }
}

void FormDebtDetailSimple::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event);
    
    QRect rectClient = this->rect();
    QPixmap pixmap(rectClient.width(), rectClient.height());
    QPainter painter(&pixmap);
    
    // 绘制背景
    DrawBackground(painter);
    
    // 绘制标题
    DrawTitle(painter);
    
    // 绘制汇总信息
    DrawSummary(painter);
    
    // 绘制债务列表
    DrawDebtList(painter);
    
    // 绘制说明信息
    DrawReason(painter);
    
    // 绘制按钮
    DrawButtons(painter);
    
    painter.end();
    
    // 绘制到窗口
    QPainter windowPainter(this);
    windowPainter.drawPixmap(rectClient, pixmap);
}

void FormDebtDetailSimple::DrawBackground(QPainter &painter)
{
    QRect rectClient = this->rect();
    
    // 填充背景色
    painter.fillRect(rectClient, m_colorBackground);
    
    // 绘制边框
    painter.setPen(QPen(m_colorBorder, 2));
    painter.drawRect(rectClient);
}

void FormDebtDetailSimple::DrawTitle(QPainter &painter)
{
    QRect titleRect(0, 0, this->width(), TITLE_HEIGHT);
    
    painter.setFont(m_fontTitle);
    painter.setPen(m_colorTitle);
    
    QString title = QString::fromUtf8("省内欠费明细");
    painter.drawText(titleRect, Qt::AlignCenter, title);
    
    // 绘制标题下划线
    painter.setPen(QPen(m_colorBorder, 1));
    painter.drawLine(MARGIN, TITLE_HEIGHT - 1, this->width() - MARGIN, TITLE_HEIGHT - 1);
}

void FormDebtDetailSimple::DrawSummary(QPainter &painter)
{
    int startY = TITLE_HEIGHT;
    QRect summaryRect(MARGIN, startY, this->width() - 2 * MARGIN, SUMMARY_HEIGHT);
    
    painter.setFont(m_fontSummary);
    painter.setPen(m_colorText);
    
    QString summaryText = QString::fromUtf8("车牌：%1  欠费记录：%2条  总金额：%3元")
                         .arg(m_debtResult.vehiclePlate.isEmpty() ? QString::fromUtf8("未知") : m_debtResult.vehiclePlate)
                         .arg(m_nTotalItems)
                         .arg(QString::number(m_nTotalAmount / 100.0, 'f', 2));
    
    painter.drawText(summaryRect, Qt::AlignLeft | Qt::AlignVCenter, summaryText);
    
    // 绘制汇总区域下划线
    painter.setPen(QPen(m_colorBorder, 1));
    painter.drawLine(MARGIN, startY + SUMMARY_HEIGHT - 1, this->width() - MARGIN, startY + SUMMARY_HEIGHT - 1);
}

void FormDebtDetailSimple::DrawDebtList(QPainter &painter)
{
    int startY = TITLE_HEIGHT + SUMMARY_HEIGHT;
    int availableHeight = this->height() - startY - BUTTON_HEIGHT - 2 * MARGIN;
    int maxVisibleItems = qMin(MAX_VISIBLE_ITEMS, availableHeight / ITEM_HEIGHT);
    
    painter.setFont(m_fontItem);
    
    if (m_nTotalItems == 0) {
        painter.setPen(m_colorWarning);
        QRect noDataRect(MARGIN, startY, this->width() - 2 * MARGIN, availableHeight);
        painter.drawText(noDataRect, Qt::AlignCenter, QString::fromUtf8("无欠费记录"));
        return;
    }
    
    // 绘制债务项目列表
    for (int i = 0; i < qMin(maxVisibleItems, m_nTotalItems); i++) {
        int itemIndex = i + m_nScrollOffset;
        if (itemIndex >= m_nTotalItems) break;
        
        const RepayDebtItem &item = m_debtResult.debtItems[itemIndex];
        
        int itemY = startY + i * ITEM_HEIGHT;
        QRect itemRect(MARGIN, itemY, this->width() - 2 * MARGIN, ITEM_HEIGHT);
        
        // 绘制项目背景
        QColor bgColor = (itemIndex == m_nCurrentIndex) ? m_colorSelectedItem : m_colorItem;
        painter.fillRect(itemRect, bgColor);
        
        // 绘制项目边框
        painter.setPen(QPen(m_colorBorder, 1));
        painter.drawRect(itemRect);
        
        // 绘制项目内容
        painter.setPen(m_colorText);
        
        QString itemText = QString::fromUtf8("%1 → %2  金额：%3元  时间：%4")
                          .arg(item.entryStation.isEmpty() ? QString::fromUtf8("未知") : item.entryStation)
                          .arg(item.exitStation.isEmpty() ? QString::fromUtf8("未知") : item.exitStation)
                          .arg(QString::number(item.debtAmount / 100.0, 'f', 2))
                          .arg(item.debtDate.isEmpty() ? QString::fromUtf8("未知") : item.debtDate);
        
        QRect textRect = itemRect.adjusted(SPACING, 0, -SPACING, 0);
        painter.drawText(textRect, Qt::AlignLeft | Qt::AlignVCenter, itemText);
    }
}

void FormDebtDetailSimple::DrawReason(QPainter &painter)
{
    int reasonY = this->height() - BUTTON_HEIGHT - 40;
    QRect reasonRect(MARGIN, reasonY, this->width() - 2 * MARGIN, 30);
    
    painter.setFont(m_fontText);
    painter.setPen(m_colorSuccess);
    
    QString reasonText = QString::fromUtf8("确认补费总金额：%1元")
                        .arg(QString::number(m_nTotalAmount / 100.0, 'f', 2));
    
    painter.drawText(reasonRect, Qt::AlignCenter, reasonText);
}

void FormDebtDetailSimple::DrawButtons(QPainter &painter)
{
    int buttonY = this->height() - BUTTON_HEIGHT - MARGIN;
    int buttonWidth = 100;
    int buttonSpacing = 20;
    int totalButtonWidth = 2 * buttonWidth + buttonSpacing;
    int startX = (this->width() - totalButtonWidth) / 2;
    
    painter.setFont(m_fontText);
    
    // 确认按钮
    QRect confirmRect(startX, buttonY, buttonWidth, BUTTON_HEIGHT - MARGIN);
    painter.fillRect(confirmRect, m_colorButton);
    painter.setPen(QPen(m_colorBorder, 2));
    painter.drawRect(confirmRect);
    painter.setPen(Qt::white);
    painter.drawText(confirmRect, Qt::AlignCenter, QString::fromUtf8("确认(F12)"));
    
    // 取消按钮
    QRect cancelRect(startX + buttonWidth + buttonSpacing, buttonY, buttonWidth, BUTTON_HEIGHT - MARGIN);
    painter.fillRect(cancelRect, QColor(128, 128, 128));
    painter.setPen(QPen(m_colorBorder, 2));
    painter.drawRect(cancelRect);
    painter.setPen(Qt::white);
    painter.drawText(cancelRect, Qt::AlignCenter, QString::fromUtf8("取消(ESC)"));
}

int FormDebtDetailSimple::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    if (!mtcKeyEvent) {
        return CBaseOpWidget::mtcKeyPressed(mtcKeyEvent);
    }
    
    int keyCode = mtcKeyEvent->func();
    
    switch (keyCode) {
        case KeyEsc:
            // 取消
            OnCancel();
            break;
            
        case KeyConfirm:
            // 确认
            ConfirmSelection();
            break;
            
        case KeyUp:
            // 向上选择
            if (m_nCurrentIndex > 0) {
                m_nCurrentIndex--;
                if (m_nCurrentIndex < m_nScrollOffset) {
                    m_nScrollOffset = m_nCurrentIndex;
                }
                update();
            }
            break;
            
        case KeyDown:
            // 向下选择
            if (m_nCurrentIndex < m_nTotalItems - 1) {
                m_nCurrentIndex++;
                if (m_nCurrentIndex >= m_nScrollOffset + MAX_VISIBLE_ITEMS) {
                    m_nScrollOffset = m_nCurrentIndex - MAX_VISIBLE_ITEMS + 1;
                }
                update();
            }
            break;
            
        default:
            return CBaseOpWidget::mtcKeyPressed(mtcKeyEvent);
    }
    
    return 1; // 已处理
}

void FormDebtDetailSimple::OnModalShowed()
{
    CBaseOpWidget::OnModalShowed();
    
    // 界面显示后的处理
    InfoLog("简化补费明细界面已显示");
}

void FormDebtDetailSimple::OnAutoCloseTimer()
{
    InfoLog("简化补费明细界面自动关闭");
    OnCancel();
}

void FormDebtDetailSimple::ConfirmSelection()
{
    if (m_nTotalItems == 0) {
        ShowWarningMessage(QString::fromUtf8("没有可确认的欠费记录"));
        return;
    }
    
    if (m_nTotalAmount <= 0) {
        ShowWarningMessage(QString::fromUtf8("补费金额无效"));
        return;
    }
    
    InfoLog(QString("用户确认补费 - 总金额:%1分").arg(m_nTotalAmount));
    OnOk();
}

void FormDebtDetailSimple::ShowWarningMessage(const QString &message)
{
    WarnLog(QString("简化补费明细界面警告：%1").arg(message));
    // 可以在这里添加界面提示逻辑，比如临时显示警告信息
}
