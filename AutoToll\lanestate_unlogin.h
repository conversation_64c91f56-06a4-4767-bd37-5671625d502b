#ifndef CLANESTATE_UNLOGIN_H
#define CLANESTATE_UNLOGIN_H
#include "abstractstate.h"
#include <QThread>

// 移动支付注册线程类
class MobilePayRegisterThread : public QThread
{
    Q_OBJECT
public:
    explicit MobilePayRegisterThread(const QString& subCenter, const QString& stationId,
                                   const QString& laneId, const QString& gbLaneId, QObject* parent = nullptr);

protected:
    void run() override;

private:
    QString m_subCenter;
    QString m_stationId;
    QString m_laneId;
    QString m_gbLaneId;

signals:
    void registerCompleted(bool success, const QString& errorMsg);
};

class CLaneState_UnLogin : public CAbstractState
{
    Q_OBJECT
public:
    explicit CLaneState_UnLogin(QObject *Parent = NULL);
    virtual ~CLaneState_UnLogin();

    bool ProcessDIEvent(qint32 nDI, bool bStatus, QString &sError);

    bool TestPrintPaper(QString &sError);

protected:
    //登录的选择的班次
    CShiftParam m_ShiftParam;
    //登录选择的收费员
    COperInfo m_OperInfo;

    bool m_bRcvPaperCard;
    bool m_bStartPaperCard;

protected:
    virtual int mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent);
    void Enter();
    void Leave();
    //班前准备操作(如票号，雨棚灯之类)
    bool DoPrepareWork();
    //选择班次
    bool SelectShift();

public:
    //上班登录操作,
    void DoLogin();
    //显示功能菜单
    void DoFuncMenu();

private slots:
    void onMobilePayRegisterCompleted(bool success, const QString& errorMsg);
};

#endif  // CLANESTATE_UNLOGIN_H
