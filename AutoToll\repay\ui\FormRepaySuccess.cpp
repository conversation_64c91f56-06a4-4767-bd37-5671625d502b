#include "FormRepaySuccess.h"
#include <QPainter>
#include <QKeyEvent>
#include <QDateTime>
#include "../../globalui.h"
#include "../../dlgmain.h"
#include "../../log4qt/ilogmsg.h"

FormRepaySuccess::FormRepaySuccess(QWidget *parent)
    : CBaseOpWidget(parent)
    , m_pLblTitle(0)
    , m_pLblVehInfo(0)
    , m_pLblPayResult(0)
    , m_pLblHelpInfo(0)
    , m_pAutoCloseTimer(0)
    , m_pCountdownTimer(0)
    , m_vehPlateColor(0)
    , m_amount(0)
    , m_autoCloseSeconds(30)
    , m_remainingSeconds(30)
{
    CreateControls();
    SetupControlProperties();
    InitConnections();
    
    filterChildrenKeyEvent();
    setObjectName("FormRepaySuccess");
}

FormRepaySuccess::~FormRepaySuccess()
{
    if (m_pAutoCloseTimer) {
        m_pAutoCloseTimer->stop();
        delete m_pAutoCloseTimer;
        m_pAutoCloseTimer = 0;
    }
    
    if (m_pCountdownTimer) {
        m_pCountdownTimer->stop();
        delete m_pCountdownTimer;
        m_pCountdownTimer = 0;
    }
}

void FormRepaySuccess::CreateControls()
{
    // 创建界面控件
    m_pLblTitle = new QLabel(QString::fromUtf8("补费成功"), this);
    m_pLblVehInfo = new QLabel(this);
    m_pLblPayResult = new QLabel(this);
    m_pLblHelpInfo = new QLabel(this);

    // 创建定时器
    m_pAutoCloseTimer = new QTimer(this);
    m_pAutoCloseTimer->setSingleShot(true);
    
    m_pCountdownTimer = new QTimer(this);
}

void FormRepaySuccess::SetupControlProperties()
{
    // 设置字体
    m_fontTitle = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TitleFontSize); // 标题字体统一
    m_fontVehInfo = QFont(g_GlobalUI.m_FontName, 18);    // 车辆信息字体
    m_fontContent = QFont(g_GlobalUI.m_FontName, 18);    // 内容字体
    m_fontHelp = QFont(g_GlobalUI.m_FontName, 12);       // 帮助信息字体
    
    // 设置背景色
    m_colorBackground = g_GlobalUI.m_ColorBackground;
    
    // 标题
    m_pLblTitle->setFont(m_fontTitle);
    m_pLblTitle->setAlignment(Qt::AlignCenter);
    m_pLblTitle->setStyleSheet("color: rgb(0, 0, 0);");
    
    // 车辆信息
    m_pLblVehInfo->setFont(m_fontVehInfo);
    m_pLblVehInfo->setAlignment(Qt::AlignCenter);
    
    // 支付结果
    m_pLblPayResult->setFont(m_fontContent);
    m_pLblPayResult->setAlignment(Qt::AlignCenter);
    m_pLblPayResult->setStyleSheet("color: rgb(0, 0, 0);");
    
    // 帮助信息
    m_pLblHelpInfo->setFont(m_fontHelp);
    m_pLblHelpInfo->setAlignment(Qt::AlignCenter);
    m_pLblHelpInfo->setStyleSheet("color: rgb(100, 100, 100);");
}

void FormRepaySuccess::InitLayout()
{
    int width = rect().width();
    int height = rect().height();
    
    // 计算布局
    int contentWidth = width - 40;  // 左右各留20像素边距
    int lineHeight = 40;
    
    // 标题位置
    int titleY = g_GlobalUI.optw_TitleHeight + 40;
    QRect titleRect(20, titleY, contentWidth, lineHeight);
    m_pLblTitle->setGeometry(titleRect);
    
    // 车辆信息位置 - 上移
    int vehInfoY = g_GlobalUI.optw_TitleHeight + 90;
    QRect vehInfoRect(20, vehInfoY, contentWidth, lineHeight);
    m_pLblVehInfo->setGeometry(vehInfoRect);
    
    // 支付结果位置 - 上移，减少间距
    int payResultY = vehInfoY + lineHeight + 12;
    QRect payResultRect(20, payResultY, contentWidth, lineHeight);
    m_pLblPayResult->setGeometry(payResultRect);
    
    // 帮助信息 - 固定在底部
    int helpY = height - 50;
    QRect helpRect(20, helpY, contentWidth, 30);
    m_pLblHelpInfo->setGeometry(helpRect);
}

void FormRepaySuccess::InitConnections()
{
    connect(m_pAutoCloseTimer, SIGNAL(timeout()), this, SLOT(OnAutoClose()));
    connect(m_pCountdownTimer, SIGNAL(timeout()), this, SLOT(OnCountdown()));
}

bool FormRepaySuccess::ShowRepaySuccess(const QString &repayTypeName, 
                                      const QString &vehPlate, 
                                      int vehPlateColor,
                                      const QString &payTypeName, 
                                      int amount,
                                      int autoCloseSeconds)
{
    // 保存参数
    m_repayTypeName = repayTypeName;
    m_vehPlate = vehPlate;
    m_vehPlateColor = vehPlateColor;
    m_payTypeName = payTypeName;
    m_amount = amount;
    m_autoCloseSeconds = autoCloseSeconds;
    m_remainingSeconds = autoCloseSeconds;
    
    // 初始化界面
    InitUI();
    InitLayout();
    
    // 设置显示内容
    QString vehColorName = GetVehPlateColorName(vehPlateColor);
    m_pLblVehInfo->setText(QString::fromUtf8("车辆【%1%2】").arg(vehColorName).arg(vehPlate));
    
    QString amountText = QString::number(amount / 100.0, 'f', 2);
    m_pLblPayResult->setText(QString::fromUtf8("%1补费成功！%2支付%3元")
                           .arg(repayTypeName)
                           .arg(payTypeName)
                           .arg(amountText));
    
    // 更新帮助提示/倒计时显示
    UpdateCountdownDisplay();
    
    // 当autoCloseSeconds<=0时，不启动自动关闭与倒计时（按键继续）
    if (autoCloseSeconds > 0) {
        m_pAutoCloseTimer->start(autoCloseSeconds * 1000);
        m_pCountdownTimer->start(1000);  // 每秒更新一次倒计时
    }
    
    InfoLog(QString("显示补费成功界面 - 车牌:%1, 金额:%2分, 支付方式:%3, 自动关闭:%4秒")
            .arg(vehPlate).arg(amount).arg(payTypeName).arg(autoCloseSeconds));
    
    // 显示模态对话框
    return (CBaseOpWidget::Rlt_OK == doModalShow());
}

void FormRepaySuccess::InitUI()
{
    CBaseOpWidget::InitUI();
}

int FormRepaySuccess::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    if (!mtcKeyEvent) return 0;

    // 处理确认键或ESC键
    mtcKeyEvent->setKeyType(KC_Func);
    if (mtcKeyEvent->func() == KeyConfirm || mtcKeyEvent->func() == KeyEsc) {
        OnConfirmClicked();
    }

    return 1;
}

void FormRepaySuccess::OnConfirmClicked()
{
    InfoLog("用户确认补费成功界面");
    
    // 停止定时器
    if (m_pAutoCloseTimer) {
        m_pAutoCloseTimer->stop();
    }
    if (m_pCountdownTimer) {
        m_pCountdownTimer->stop();
    }
    
    OnOk();
}

void FormRepaySuccess::OnAutoClose()
{
    InfoLog("补费成功界面自动关闭");
    
    // 停止倒计时定时器
    if (m_pCountdownTimer) {
        m_pCountdownTimer->stop();
    }
    
    OnOk();
}

void FormRepaySuccess::OnCountdown()
{
    m_remainingSeconds--;
    UpdateCountdownDisplay();
    
    if (m_remainingSeconds <= 0) {
        if (m_pCountdownTimer) {
            m_pCountdownTimer->stop();
        }
    }
}

void FormRepaySuccess::UpdateCountdownDisplay()
{
    if (m_autoCloseSeconds <= 0) {
        // 无倒计时模式
        m_pLblHelpInfo->setText(QString::fromUtf8("按【确认】或【取消】键继续"));
    } else {
        m_pLblHelpInfo->setText(QString::fromUtf8("按【确认】或【取消】键继续，%1秒后自动关闭").arg(m_remainingSeconds));
    }
}

QString FormRepaySuccess::GetVehPlateColorName(int color)
{
    switch (color) {
        case 0: return QString::fromUtf8("蓝");
        case 1: return QString::fromUtf8("黄");
        case 2: return QString::fromUtf8("黑");
        case 3: return QString::fromUtf8("白");
        case 4: return QString::fromUtf8("绿");
        default: return QString("");
    }
}

void FormRepaySuccess::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event);
    
    QRect rectClient = this->rect();
    QPixmap pixmap(rectClient.width(), rectClient.height());
    QPainter painter(&pixmap);

    // 绘制背景
    painter.setBrush(m_colorBackground);
    painter.setPen(Qt::black);
    painter.drawRect(rectClient.x(), rectClient.y(), rectClient.width() - 1, rectClient.height() - 1);

    painter.end();
    QPainter ptThis(this);
    ptThis.drawPixmap(rectClient, pixmap);
}
