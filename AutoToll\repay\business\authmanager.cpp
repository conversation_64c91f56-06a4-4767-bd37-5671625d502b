#include "authmanager.h"
#include "../config/repayconfig.h"
#include "../database/repay_db_manager.h"
#include "../../log4qt/ilogmsg.h"
#include <QMutexLocker>
#include <QRegExp>

AuthManager* AuthManager::m_pInstance = 0;
QMutex AuthManager::m_mutex;

AuthManager::AuthManager(QObject *parent)
    : QObject(parent)
    , m_bAuthorized(false)
    , m_nAuthTimeoutSecs(300)  // 默认5分钟
    , m_nMaxRetryTimes(3)
    , m_bRequireAuth(true)
{
    m_pAuthTimer = new QTimer(this);
    m_pAuthTimer->setSingleShot(true);
    connect(m_pAuthTimer, SIGNAL(timeout()), this, SLOT(OnAuthTimeout()));
    
    m_pWarningTimer = new QTimer(this);
    m_pWarningTimer->setSingleShot(true);
    connect(m_pWarningTimer, SIGNAL(timeout()), this, SLOT(OnAuthWarning()));
    
    // 加载配置
    LoadAuthConfig();
}

AuthManager* AuthManager::GetInstance()
{
    if (m_pInstance == 0) {
        QMutexLocker locker(&m_mutex);
        if (m_pInstance == 0) {
            m_pInstance = new AuthManager();
        }
    }
    return m_pInstance;
}

bool AuthManager::ValidateOperator(const QString &operatorId, const QString &password)
{
    if (operatorId.isEmpty() || password.isEmpty()) {
        LogAuthOperation(operatorId, false, "操作员ID或密码为空");
        return false;
    }
    
    // 检查是否被锁定
    if (m_lockTimeMap.contains(operatorId)) {
        QDateTime lockTime = m_lockTimeMap.value(operatorId);
        if (lockTime.secsTo(QDateTime::currentDateTime()) < 300) { // 5分钟锁定
            LogAuthOperation(operatorId, false, "操作员账户被锁定");
            return false;
        } else {
            // 锁定时间已过，清除锁定
            m_lockTimeMap.remove(operatorId);
            m_retryCountMap.remove(operatorId);
        }
    }
    
    // 使用项目现有的操作员验证功能
    COperTable *pOperTable = (COperTable *)CParamFileMgr::GetParamFile(cfOper);
    if (!pOperTable) {
        LogAuthOperation(operatorId, false, "操作员参数文件未加载");
        return false;
    }
    
    COperInfo operInfo;
    QString sError;
    quint32 dwOper = operatorId.toUInt();
    qint32 nStationId = Ptr_Info->GetStationID();
    
    // 验证操作员和密码
    bool bResult = pOperTable->VerifyOperatorInput(nStationId, dwOper, password, operInfo, sError);
    
    if (!bResult) {
        // 增加重试次数
        int retryCount = m_retryCountMap.value(operatorId, 0) + 1;
        m_retryCountMap[operatorId] = retryCount;
        
        if (retryCount >= m_nMaxRetryTimes) {
            // 锁定账户
            m_lockTimeMap[operatorId] = QDateTime::currentDateTime();
            LogAuthOperation(operatorId, false, QString("验证失败超过%1次，账户被锁定").arg(m_nMaxRetryTimes));
        } else {
            LogAuthOperation(operatorId, false, QString("验证失败，剩余%1次尝试，原因：%2").arg(m_nMaxRetryTimes - retryCount).arg(sError));
        }
        return false;
    }
    
    // 检查班长权限 (wOperType: 1-收费员 2-班长)
    if (operInfo.wOperType != 2) {
        // 检查是否具有特情授权权限
        int nSpecialRole = operInfo.GetOperRole(OR_Special);
        if (nSpecialRole != OR_Special) {
            LogAuthOperation(operatorId, false, "操作员没有班长权限或特情授权权限");
            return false;
        }
    }
    
    // 验证成功，清除重试计数
    m_retryCountMap.remove(operatorId);
    LogAuthOperation(operatorId, true, QString("授权验证成功，操作员：%1").arg(operInfo.sOperName));
    
    return true;
}

bool AuthManager::CheckManagerPermission(const QString &operatorId)
{
    COperTable *pOperTable = (COperTable *)CParamFileMgr::GetParamFile(cfOper);
    if (!pOperTable) {
        return false;
    }
    
    COperInfo operInfo;
    QString sError;
    quint32 dwOper = operatorId.toUInt();
    
    if (!pOperTable->GetOperInfo(dwOper, operInfo, sError)) {
        return false;
    }
    
    // 班长权限：wOperType == 2 或者具有特情授权权限
    if (operInfo.wOperType == 2) {
        return true;
    }
    
    // 检查特情授权权限
    int nSpecialRole = operInfo.GetOperRole(OR_Special);
    return (nSpecialRole == OR_Special);
}

bool AuthManager::IsAuthorizationValid()
{
    if (!m_bRequireAuth) {
        return true;  // 如果不需要授权，则总是有效
    }
    
    if (!m_bAuthorized) {
        return false;
    }
    
    if (m_authStartTime.isNull()) {
        return false;
    }
    
    int elapsedSecs = m_authStartTime.secsTo(QDateTime::currentDateTime());
    return elapsedSecs < m_nAuthTimeoutSecs;
}

void AuthManager::StartAuthSession(const QString &operatorId)
{
    m_sCurrentOperator = operatorId;
    m_authStartTime = QDateTime::currentDateTime();
    m_bAuthorized = true;
    
    // 启动超时定时器
    m_pAuthTimer->start(m_nAuthTimeoutSecs * 1000);
    
    // 启动警告定时器（提前1分钟）
    int warningTime = m_nAuthTimeoutSecs - 60;
    if (warningTime > 0) {
        m_pWarningTimer->start(warningTime * 1000);
    }
    
    InfoLog(QString("授权会话已开始 - 操作员:%1, 超时时间:%2秒").arg(operatorId).arg(m_nAuthTimeoutSecs));
    
    // 记录授权操作
    AuthorizationRecord record;
    record.operatorId = operatorId;
    record.authType = 1; // 补费授权
    record.authTime = m_authStartTime;
    record.result = 1; // 成功
    record.clientInfo = "补费系统授权";
    record.remark = "授权会话开始";
    
    RepayDbManager::GetInstance()->LogAuthorization(record);
    
    emit AuthSessionStarted(operatorId);
}

void AuthManager::EndAuthSession()
{
    if (m_bAuthorized) {
        InfoLog(QString("授权会话已结束 - 操作员:%1").arg(m_sCurrentOperator));
        
        // 记录授权结束
        AuthorizationRecord record;
        record.operatorId = m_sCurrentOperator;
        record.authType = 1; // 补费授权
        record.authTime = QDateTime::currentDateTime();
        record.result = 1; // 成功
        record.clientInfo = "补费系统授权";
        record.remark = "授权会话结束";
        
        RepayDbManager::GetInstance()->LogAuthorization(record);
    }
    
    m_sCurrentOperator.clear();
    m_authStartTime = QDateTime();
    m_bAuthorized = false;
    
    m_pAuthTimer->stop();
    m_pWarningTimer->stop();
    
    emit AuthSessionEnded();
}

QString AuthManager::GetCurrentAuthorizedOperator() const
{
    return m_bAuthorized ? m_sCurrentOperator : QString();
}

int AuthManager::GetRemainingAuthTime() const
{
    if (!m_bAuthorized || m_authStartTime.isNull()) {
        return 0;
    }
    
    int elapsedSecs = m_authStartTime.secsTo(QDateTime::currentDateTime());
    int remainingSecs = m_nAuthTimeoutSecs - elapsedSecs;
    
    return qMax(0, remainingSecs);
}

void AuthManager::ExtendAuthSession()
{
    if (m_bAuthorized) {
        m_authStartTime = QDateTime::currentDateTime();
        
        // 重新启动定时器
        m_pAuthTimer->start(m_nAuthTimeoutSecs * 1000);
        
        int warningTime = m_nAuthTimeoutSecs - 60;
        if (warningTime > 0) {
            m_pWarningTimer->start(warningTime * 1000);
        }
        
        InfoLog(QString("授权会话已延长 - 操作员:%1").arg(m_sCurrentOperator));
    }
}

bool AuthManager::ValidatePasswordStrength(const QString &password)
{
    if (password.length() < 6) {
        return false;
    }
    
    // 检查是否包含数字和字母
    QRegExp hasDigit("\\d");
    QRegExp hasLetter("[a-zA-Z]");
    
    return hasDigit.indexIn(password) != -1 && hasLetter.indexIn(password) != -1;
}

void AuthManager::LoadAuthConfig()
{
    RepayConfig *config = RepayConfig::GetInstance();
    if (config) {
        m_bRequireAuth = config->IsRequireAuth();
        m_nMaxRetryTimes = config->GetMaxRetryTimes();
        m_nAuthTimeoutSecs = config->GetAuthTimeoutSecs();
        
        InfoLog(QString("授权配置加载完成 - 需要授权:%1, 超时时间:%2秒, 最大重试:%3次")
                .arg(m_bRequireAuth ? "是" : "否")
                .arg(m_nAuthTimeoutSecs)
                .arg(m_nMaxRetryTimes));
    }
}

void AuthManager::OnAuthTimeout()
{
    WarnLog(QString("授权会话超时 - 操作员:%1").arg(m_sCurrentOperator));
    
    EndAuthSession();
    emit AuthSessionTimeout();
}

void AuthManager::OnAuthWarning()
{
    InfoLog(QString("授权会话即将超时 - 操作员:%1, 剩余时间:1分钟").arg(m_sCurrentOperator));
    emit AuthSessionWarning();
}



void AuthManager::LogAuthOperation(const QString &operatorId, bool success, const QString &reason)
{
    QString logMessage = QString("授权操作 - 操作员:%1, 结果:%2")
                        .arg(operatorId)
                        .arg(success ? "成功" : "失败");
    
    if (!reason.isEmpty()) {
        logMessage += QString(", 原因:%1").arg(reason);
    }
    
    if (success) {
        InfoLog(logMessage);
    } else {
        WarnLog(logMessage);
    }
    
    // 记录到数据库
    AuthorizationRecord record;
    record.operatorId = operatorId;
    record.authType = 1; // 补费授权
    record.authTime = QDateTime::currentDateTime();
    record.result = success ? 1 : 0;
    record.clientInfo = "补费系统授权验证";
    record.remark = reason.isEmpty() ? (success ? "验证成功" : "验证失败") : reason;
    
    RepayDbManager::GetInstance()->LogAuthorization(record);
} 