#ifndef FORMDEBTDETAILSIMPLE_H
#define FORMDEBTDETAILSIMPLE_H

#include <QWidget>
#include <QLabel>
#include <QPushButton>
#include <QTimer>
#include <QPainter>
#include <QKeyEvent>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>

#include "../../baseopwidget.h"
#include "../../MtcKey/MtcKeyDef.h"
#include "../../globalui.h"
#include "../common/repaytypes.h"

/**
 * @brief 简化的补费明细显示界面
 * 用于显示省内欠费明细的简化版本，专门为新版补费功能设计
 */
class FormDebtDetailSimple : public CBaseOpWidget
{
    Q_OBJECT

public:
    explicit FormDebtDetailSimple(QWidget *parent = 0);
    ~FormDebtDetailSimple();

    // 初始化界面
    virtual void InitUI(int iFlag = 0);
    
    // 显示明细界面
    bool ShowDebtDetail(const RepayDebtQueryResult &result);
    
    // 获取总补费金额
    int GetTotalRepayAmount() const;

protected:
    // 重写绘制事件
    void paintEvent(QPaintEvent *event);
    
    // 重写按键事件处理
    virtual int mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent);
    
    // 重写模态显示后的处理
    virtual void OnModalShowed();

private slots:
    // 自动关闭定时器
    void OnAutoCloseTimer();

private:
    // 界面初始化
    void InitUIConfig();
    
    // 绘制函数
    void DrawBackground(QPainter &painter);
    void DrawTitle(QPainter &painter);
    void DrawSummary(QPainter &painter);
    void DrawDebtList(QPainter &painter);
    void DrawReason(QPainter &painter);
    void DrawButtons(QPainter &painter);
    
    // 业务处理
    void ConfirmSelection();
    void ShowWarningMessage(const QString &message);
    
    // 数据处理
    void SetDebtResult(const RepayDebtQueryResult &result);
    void UpdateSummaryInfo();

private:
    // 债务查询结果
    RepayDebtQueryResult m_debtResult;
    
    // 界面状态
    int m_nCurrentIndex;              // 当前选中项
    int m_nScrollOffset;              // 滚动偏移
    int m_nTotalItems;                // 总项目数
    int m_nTotalAmount;               // 总金额（分）
    
    // 定时器
    QTimer *m_pAutoCloseTimer;        // 自动关闭定时器
    
    // 界面配置
    QFont m_fontTitle;                // 标题字体
    QFont m_fontText;                 // 普通文本字体
    QFont m_fontItem;                 // 项目字体
    QFont m_fontSummary;              // 汇总字体
    
    // 颜色配置
    QColor m_colorBackground;         // 背景色
    QColor m_colorTitle;              // 标题色
    QColor m_colorText;               // 文本色
    QColor m_colorItem;               // 项目背景色
    QColor m_colorSelectedItem;       // 选中项目色
    QColor m_colorBorder;             // 边框色
    QColor m_colorButton;             // 按钮色
    QColor m_colorSuccess;            // 成功色
    QColor m_colorWarning;            // 警告色
    
    // 布局常量
    static const int TITLE_HEIGHT = 50;      // 标题高度
    static const int SUMMARY_HEIGHT = 60;    // 汇总信息高度
    static const int ITEM_HEIGHT = 32;       // 项目高度（统一到更紧凑以容纳更多信息）
    static const int BUTTON_HEIGHT = 50;     // 按钮高度
    static const int MARGIN = 10;            // 边距
    static const int SPACING = 5;            // 间距
    static const int MAX_VISIBLE_ITEMS = 5;  // 最大可见项目数
    
    // 自动关闭时间（秒）
    static const int AUTO_CLOSE_TIMEOUT = 30;
};

#endif // FORMDEBTDETAILSIMPLE_H
