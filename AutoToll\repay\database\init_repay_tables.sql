-- 江西省内补费业务数据库初始化脚本
-- 创建日期：2025年01月18日
-- 说明：创建补费操作日志表和授权日志表

-- 补费操作日志表
CREATE TABLE IF NOT EXISTS RepayOperationLog (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    operatorId VARCHAR(20) NOT NULL COMMENT '操作员ID',
    operationType INTEGER NOT NULL COMMENT '操作类型(1-当趟补费,2-省内名单补费)',
    vehPlate VARCHAR(20) NOT NULL COMMENT '车牌号',
    vehPlateColor INTEGER NOT NULL COMMENT '车牌颜色(0-蓝,1-黄,2-黑,3-白,4-绿等)',
    repayAmount INTEGER NOT NULL COMMENT '补费金额(分)',
    paymentType INTEGER NOT NULL COMMENT '支付方式(1-现金,2-ETC,3-移动支付等)',
    listno VARCHAR(50) COMMENT '省中心查询编号',
    orderIds TEXT COMMENT '关联的订单ID列表(JSON格式)',
    wasteId VARCHAR(50) COMMENT '生成的补费流水号',
    operationTime DATETIME NOT NULL COMMENT '操作时间',
    result INTEGER NOT NULL COMMENT '操作结果(0-失败,1-成功,2-部分成功)',
    remark TEXT COMMENT '备注信息(失败原因、特殊情况说明等)'
);

-- 授权操作日志表
CREATE TABLE IF NOT EXISTS AuthorizationLog (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    operatorId VARCHAR(20) NOT NULL COMMENT '请求授权的操作员ID',
    authType INTEGER NOT NULL COMMENT '授权类型(1-补费授权,2-管理授权等)',
    authTime DATETIME NOT NULL COMMENT '授权时间',
    result INTEGER NOT NULL COMMENT '授权结果(0-失败,1-成功,2-超时)',
    clientInfo VARCHAR(100) COMMENT '客户端信息(车道编号、工作站等)',
    remark TEXT COMMENT '备注信息(失败原因等)'
);

-- 创建索引以提升查询性能
-- 补费日志按操作员和时间查询的复合索引
CREATE INDEX IF NOT EXISTS idx_repay_log_operator_time 
ON RepayOperationLog(operatorId, operationTime DESC);

-- 补费日志按车牌查询的索引
CREATE INDEX IF NOT EXISTS idx_repay_log_vehicle 
ON RepayOperationLog(vehPlate, vehPlateColor);

-- 补费日志按操作时间查询的索引（用于定期清理）
CREATE INDEX IF NOT EXISTS idx_repay_log_time 
ON RepayOperationLog(operationTime);

-- 补费日志按流水号查询的索引
CREATE INDEX IF NOT EXISTS idx_repay_log_waste 
ON RepayOperationLog(wasteId);

-- 授权日志按操作员和时间查询的复合索引
CREATE INDEX IF NOT EXISTS idx_auth_log_operator_time 
ON AuthorizationLog(operatorId, authTime DESC);

-- 授权日志按时间查询的索引（用于定期清理）
CREATE INDEX IF NOT EXISTS idx_auth_log_time 
ON AuthorizationLog(authTime);

-- 插入测试数据（仅用于开发测试）
-- 补费操作日志测试数据
INSERT OR IGNORE INTO RepayOperationLog 
(id, operatorId, operationType, vehPlate, vehPlateColor, repayAmount, paymentType, 
 listno, orderIds, wasteId, operationTime, result, remark)
VALUES 
(1, 'OP001', 1, '赣A12345', 0, 500, 1, 'LST202501180001', 
 '["ORDER001","ORDER002"]', 'WID202501180001', 
 datetime('now', 'localtime'), 1, '当趟补费测试记录'),
(2, 'OP002', 2, '赣B67890', 1, 1200, 3, 'LST202501180002', 
 '["ORDER003"]', 'WID202501180002', 
 datetime('now', 'localtime'), 1, '省内名单补费测试记录');

-- 授权操作日志测试数据
INSERT OR IGNORE INTO AuthorizationLog 
(id, operatorId, authType, authTime, result, clientInfo, remark)
VALUES 
(1, 'OP001', 1, datetime('now', 'localtime'), 1, 'Lane001-Station01', '补费授权成功'),
(2, 'OP002', 1, datetime('now', 'localtime'), 1, 'Lane002-Station01', '补费授权成功');

-- 创建视图以便于数据查询和统计

-- 补费操作统计视图
CREATE VIEW IF NOT EXISTS RepayOperationSummary AS
SELECT 
    operatorId,
    operationType,
    COUNT(*) as totalOperations,
    SUM(repayAmount) as totalAmount,
    SUM(CASE WHEN result = 1 THEN 1 ELSE 0 END) as successCount,
    SUM(CASE WHEN result = 0 THEN 1 ELSE 0 END) as failCount,
    DATE(operationTime) as operationDate
FROM RepayOperationLog
GROUP BY operatorId, operationType, DATE(operationTime);

-- 每日补费统计视图
CREATE VIEW IF NOT EXISTS DailyRepaySummary AS
SELECT 
    DATE(operationTime) as repayDate,
    operationType,
    COUNT(*) as totalOperations,
    SUM(repayAmount) as totalAmount,
    SUM(CASE WHEN result = 1 THEN repayAmount ELSE 0 END) as successAmount,
    AVG(repayAmount) as avgAmount
FROM RepayOperationLog
WHERE result = 1
GROUP BY DATE(operationTime), operationType;

-- 授权成功率统计视图
CREATE VIEW IF NOT EXISTS AuthSuccessRateSummary AS
SELECT 
    operatorId,
    authType,
    COUNT(*) as totalAttempts,
    SUM(CASE WHEN result = 1 THEN 1 ELSE 0 END) as successCount,
    ROUND(100.0 * SUM(CASE WHEN result = 1 THEN 1 ELSE 0 END) / COUNT(*), 2) as successRate,
    DATE(authTime) as authDate
FROM AuthorizationLog
GROUP BY operatorId, authType, DATE(authTime);

-- 数据清理过程（用于定期清理过期日志）
-- 注意：实际使用时可能需要根据具体需求调整保留天数

-- 清理90天前的补费操作日志
-- DELETE FROM RepayOperationLog WHERE operationTime < datetime('now', '-90 days');

-- 清理90天前的授权日志
-- DELETE FROM AuthorizationLog WHERE authTime < datetime('now', '-90 days');

-- 脚本执行完成标记
INSERT OR REPLACE INTO SystemConfig (configKey, configValue, updateTime) 
VALUES ('RepayDatabase_Initialized', '1', datetime('now', 'localtime'));

COMMIT; 