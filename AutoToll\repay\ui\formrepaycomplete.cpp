#include "formrepaycomplete.h"
#include <QPainter>
#include <QKeyEvent>
#include <QDateTime>
#include "../../globalui.h"
#include "../../dlgmain.h"
#include "../../log4qt/ilogmsg.h"

FormRepayComplete::FormRepayComplete(QWidget *parent)
    : CBaseOpWidget(parent)
    , m_pLblTitle(0)
    , m_pLblSuccessIcon(0)
    , m_pLblVehPlate(0)
    , m_pLblVehType(0)
    , m_pLblPayType(0)
    , m_pLblAmount(0)
    , m_pLblRepayType(0)
    , m_pLblDateTime(0)
    , m_pLblHelpInfo(0)
    , m_vehType(1)
    , m_payType(TransPT_Cash)
    , m_amount(0)
    , m_repayType(RepayType_Current)
    , m_pAutoCloseTimer(0)
{
    CreateControls();
    SetupControlProperties();
    InitConnections();
    
    filterChildrenKeyEvent();
    setObjectName("FormRepayComplete");
}

FormRepayComplete::~FormRepayComplete()
{
    // Qt会自动清理子控件
    if (m_pAutoCloseTimer) {
        m_pAutoCloseTimer->stop();
        delete m_pAutoCloseTimer;
        m_pAutoCloseTimer = 0;
    }
}

void FormRepayComplete::CreateControls()
{
    // 创建界面控件
    m_pLblTitle = new QLabel(QString::fromUtf8("补费完成"), this);
    m_pLblSuccessIcon = new QLabel("✓", this);
    m_pLblVehPlate = new QLabel(this);
    m_pLblVehType = new QLabel(this);
    m_pLblPayType = new QLabel(this);
    m_pLblAmount = new QLabel(this);
    m_pLblRepayType = new QLabel(this);
    m_pLblDateTime = new QLabel(this);
    m_pLblHelpInfo = new QLabel(this);
    
    // 创建定时器
    m_pAutoCloseTimer = new QTimer(this);
}

void FormRepayComplete::SetupControlProperties()
{
    // 设置字体
    m_fontTitle = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TitleFontSize);
    m_fontLabel = QFont(g_GlobalUI.m_FontName, 16);
    m_fontValue = QFont(g_GlobalUI.m_FontName, 18, QFont::Bold);
    m_fontHelp = QFont(g_GlobalUI.m_FontName, 12);
    
    // 设置颜色
    m_colorBackground = g_GlobalUI.m_ColorBackground;
    m_colorSuccess = QColor(0, 150, 0);
    
    // 标题
    m_pLblTitle->setFont(m_fontTitle);
    m_pLblTitle->setAlignment(Qt::AlignCenter);
    m_pLblTitle->setStyleSheet("color: rgb(0, 150, 0);");
    
    // 成功图标
    m_pLblSuccessIcon->setFont(QFont(g_GlobalUI.m_FontName, 48, QFont::Bold));
    m_pLblSuccessIcon->setAlignment(Qt::AlignCenter);
    m_pLblSuccessIcon->setStyleSheet("color: rgb(0, 150, 0);");
    
    // 各信息标签
    m_pLblVehPlate->setFont(m_fontValue);
    m_pLblVehPlate->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    
    m_pLblVehType->setFont(m_fontLabel);
    m_pLblVehType->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    
    m_pLblPayType->setFont(m_fontLabel);
    m_pLblPayType->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    
    m_pLblAmount->setFont(m_fontValue);
    m_pLblAmount->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    m_pLblAmount->setStyleSheet("color: rgb(200, 0, 0);");
    
    m_pLblRepayType->setFont(m_fontLabel);
    m_pLblRepayType->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    
    m_pLblDateTime->setFont(m_fontLabel);
    m_pLblDateTime->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    m_pLblDateTime->setStyleSheet("color: rgb(100, 100, 100);");
    
    // 帮助信息
    m_pLblHelpInfo->setFont(m_fontHelp);
    m_pLblHelpInfo->setAlignment(Qt::AlignCenter);
    m_pLblHelpInfo->setWordWrap(false);
    m_pLblHelpInfo->setStyleSheet("color: rgb(100, 100, 100);");
    m_pLblHelpInfo->setText(QString::fromUtf8("按【确认】键完成，10秒后自动关闭"));
}

void FormRepayComplete::InitLayout()
{
    int width = rect().width();
    int height = rect().height();
    
    // 标题
    m_pLblTitle->setGeometry(0, 20, width, 50);
    
    // 成功图标
    m_pLblSuccessIcon->setGeometry(0, 80, width, 80);
    
    // 信息显示区域
    int infoStartY = 180;
    int leftMargin = 100;
    int rowHeight = 35;
    int infoWidth = width - 2 * leftMargin;
    
    // 车牌信息
    m_pLblVehPlate->setGeometry(leftMargin, infoStartY, infoWidth, rowHeight);
    infoStartY += rowHeight + 5;
    
    // 车型信息
    m_pLblVehType->setGeometry(leftMargin, infoStartY, infoWidth, rowHeight);
    infoStartY += rowHeight + 5;
    
    // 补费类型
    m_pLblRepayType->setGeometry(leftMargin, infoStartY, infoWidth, rowHeight);
    infoStartY += rowHeight + 5;
    
    // 支付方式
    m_pLblPayType->setGeometry(leftMargin, infoStartY, infoWidth, rowHeight);
    infoStartY += rowHeight + 5;
    
    // 补费金额
    m_pLblAmount->setGeometry(leftMargin, infoStartY, infoWidth, rowHeight);
    infoStartY += rowHeight + 10;
    
    // 完成时间
    m_pLblDateTime->setGeometry(leftMargin, infoStartY, infoWidth, rowHeight);
    
    // 帮助信息 - 底部居中
    m_pLblHelpInfo->setGeometry(20, height - 60, width - 40, 30);
}

void FormRepayComplete::InitConnections()
{
    // 连接定时器信号
    connect(m_pAutoCloseTimer, SIGNAL(timeout()), this, SLOT(OnAutoClose()));
}

bool FormRepayComplete::ShowRepayComplete(const QString &vehPlate, int vehType, CTransPayType payType, 
                                         int amount, RepayType repayType)
{
    // 保存参数
    m_vehPlate = vehPlate;
    m_vehType = vehType;
    m_payType = payType;
    m_amount = amount;
    m_repayType = repayType;
    
    // 初始化界面
    InitUI();
    InitLayout();
    
    // 设置显示内容
    m_pLblVehPlate->setText(QString::fromUtf8("车牌号码：%1").arg(vehPlate));
    m_pLblVehType->setText(QString::fromUtf8("车    型：%1").arg(GetVehTypeName(vehType)));
    m_pLblRepayType->setText(QString::fromUtf8("补费类型：%1").arg(GetRepayTypeName(repayType)));
    m_pLblPayType->setText(QString::fromUtf8("支付方式：%1").arg(GetPayTypeName(payType)));
    m_pLblAmount->setText(QString::fromUtf8("补费金额：%1 元").arg(QString::number(amount / 100.0, 'f', 2)));
    m_pLblDateTime->setText(QString::fromUtf8("完成时间：%1").arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss")));
    
    // 启动自动关闭定时器（10秒）
    m_pAutoCloseTimer->start(10000);
    
    InfoLog(QString("显示补费完成界面 - 车牌:%1, 金额:%2分, 支付方式:%3")
            .arg(vehPlate).arg(amount).arg(payType));
    
    // 显示模态对话框
    return (CBaseOpWidget::Rlt_OK == doModalShow());
}

void FormRepayComplete::InitUI()
{
    CBaseOpWidget::InitUI();
}

int FormRepayComplete::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    if (!mtcKeyEvent) return 0;

    // 处理确认键
    mtcKeyEvent->setKeyType(KC_Func);
    if (mtcKeyEvent->func() == KeyConfirm || mtcKeyEvent->func() == KeyEsc) {
        OnConfirmClicked();
    }

    return 1;
}

void FormRepayComplete::OnConfirmClicked()
{
    InfoLog("用户确认补费完成界面");
    
    // 停止自动关闭定时器
    if (m_pAutoCloseTimer) {
        m_pAutoCloseTimer->stop();
    }
    
    OnOk();
}

void FormRepayComplete::OnAutoClose()
{
    InfoLog("补费完成界面自动关闭");
    
    // 停止定时器
    if (m_pAutoCloseTimer) {
        m_pAutoCloseTimer->stop();
    }
    
    OnOk();
}

void FormRepayComplete::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event);
    
    QRect rectClient = this->rect();
    QPixmap pixmap(rectClient.width(), rectClient.height());
    QPainter painter(&pixmap);

    // 绘制背景
    painter.setBrush(m_colorBackground);
    painter.setPen(Qt::black);
    painter.drawRect(rectClient.x(), rectClient.y(), rectClient.width() - 1, rectClient.height() - 1);

    painter.end();
    QPainter ptThis(this);
    ptThis.drawPixmap(rectClient, pixmap);
}

QString FormRepayComplete::GetPayTypeName(CTransPayType payType)
{
    switch (payType) {
        case TransPT_Cash:
            return QString::fromUtf8("现金");
        case TransPT_ETCCard:
            return QString::fromUtf8("ETC卡");
        case TransPT_Union:
            return QString::fromUtf8("移动支付");
        case TransPT_AliPay:
            return QString::fromUtf8("支付宝");
        case TransPT_WeChat:
            return QString::fromUtf8("微信支付");
        case TransPT_Other:
            return QString::fromUtf8("其他");
        default:
            return QString::fromUtf8("未知");
    }
}

QString FormRepayComplete::GetVehTypeName(int vehType)
{
    switch (vehType) {
        case 1: return QString::fromUtf8("客1");
        case 2: return QString::fromUtf8("客2");
        case 3: return QString::fromUtf8("客3");
        case 4: return QString::fromUtf8("客4");
        case 11: return QString::fromUtf8("货1");
        case 12: return QString::fromUtf8("货2");
        case 13: return QString::fromUtf8("货3");
        case 14: return QString::fromUtf8("货4");
        case 15: return QString::fromUtf8("货5");
        case 16: return QString::fromUtf8("货6");
        default: return QString::fromUtf8("类型%1").arg(vehType);
    }
}

QString FormRepayComplete::GetRepayTypeName(RepayType repayType)
{
    switch (repayType) {
        case RepayType_Current:
            return QString::fromUtf8("当趟补费");
        case RepayType_Province:
            return QString::fromUtf8("省内名单补费");
        default:
            return QString::fromUtf8("未知类型");
    }
}


