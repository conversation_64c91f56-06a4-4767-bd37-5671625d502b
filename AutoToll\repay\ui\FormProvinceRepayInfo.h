#ifndef FORMPROVINCEREPAYINFO_H
#define FORMPROVINCEREPAYINFO_H

#include <QWidget>
#include <QLabel>
#include <QTableWidget>
#include <QScrollArea>
#include <QTimer>
#include "baseopwidget.h"
#include "../common/repaytypes.h"
#include "../common/repaytypes.h"

/**
 * @brief 省内名单补费信息界面
 * 显示当前车辆的补费详细信息，包括各收费站的欠费记录
 */
class FormProvinceRepayInfo : public CBaseOpWidget
{
    Q_OBJECT

public:
    explicit FormProvinceRepayInfo(QWidget *parent = 0);
    ~FormProvinceRepayInfo();

    /**
     * @brief 显示省内名单补费信息界面
     * @param vehPlate 车牌号
     * @param vehPlateColor 车牌颜色
     * @param result 已经查询完成的欠费结果（同步传入）
     * @return true-用户确认，false-用户取消
     */
    bool ShowRepayInfo(const QString &vehPlate, int vehPlateColor, const RepayDebtQueryResult &result);

    /**
     * @brief 获取当前选中的欠费记录索引
     * @return 选中索引
     */
    int GetSelectedIndex() const { return m_selectedIndex; }

    /**
     * @brief 获取补费总金额
     * @return 总金额（分）
     */
    int GetTotalAmount() const { return m_totalAmount; }

protected:
    // 界面初始化
    void InitUI();
    void CreateControls();
    void SetupControlProperties();
    void InitLayout();
    void InitConnections();

    // 事件处理
    int mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent);
    void paintEvent(QPaintEvent *event);

    // 数据更新
    void UpdateDebtTable();
    void UpdateSelectedRow();
    void UpdateReasonDescription();

private slots:
    void OnConfirmClicked();
    void OnCancelClicked();

private:
    // 辅助方法
    QString GetVehPlateColorName(int color);
    QString FormatDateTime(const QString &datetime);
    QString FormatAmount(int amount);

private:
    // 界面控件
    QLabel *m_pLblTitle;           // 标题：【车牌】补费总金额
    QLabel *m_pLblTableHeader1;    // 表头：出口收费站
    QLabel *m_pLblTableHeader2;    // 表头：时间
    QLabel *m_pLblTableHeader3;    // 表头：补费金额
    QScrollArea *m_pScrollArea;    // 滚动区域
    QWidget *m_pTableContent;      // 表格内容容器
    QLabel *m_pLblReasonTitle;     // 原因描述标题
    QLabel *m_pLblReasonContent;   // 原因描述内容
    QLabel *m_pLblHelpInfo;        // 帮助信息

    // 表格行控件列表
    QList<QLabel*> m_rowLabels1;   // 出口收费站列
    QList<QLabel*> m_rowLabels2;   // 时间列
    QList<QLabel*> m_rowLabels3;   // 补费金额列

    // 数据
    QString m_vehPlate;            // 车牌号
    int m_vehPlateColor;           // 车牌颜色
    RepayDebtQueryResult m_debtResult;  // 欠费查询结果
    int m_selectedIndex;           // 当前选中行
    int m_totalAmount;             // 总金额（分）
    
    // 状态标志
    bool m_bDataLoaded;            // 数据已加载

    // 样式
    QFont m_fontTitle;
    QFont m_fontHeader;
    QFont m_fontContent;
    QFont m_fontHelp;
    QColor m_colorBackground;
    QColor m_colorSelected;        // 选中行颜色
};

#endif // FORMPROVINCEREPAYINFO_H
