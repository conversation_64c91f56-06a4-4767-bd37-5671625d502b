#ifndef REPAY_DB_MANAGER_H
#define REPAY_DB_MANAGER_H

#include <QObject>
#include <QMutex>
#include <QString>
#include <QDateTime>
#include <QList>
#include "../common/repaytypes.h"

/**
 * @brief 补费数据库管理器
 * 
 * 负责管理补费相关的数据库操作，包括：
 * - 数据库初始化和表创建
 * - 补费操作日志记录
 * - 授权日志记录
 * - 数据查询和统计
 * - 数据清理和维护
 * - 数据库备份
 */
class RepayDbManager : public QObject
{
    Q_OBJECT
    
public:
    static RepayDbManager* GetInstance();
    
    /**
     * @brief 初始化数据库管理器
     * @param dbPath 数据库文件路径，为空则使用默认路径
     * @return 初始化成功返回true，失败返回false
     */
    bool Initialize(const QString &dbPath = QString());
    
    /**
     * @brief 记录补费操作日志
     * @param record 补费操作记录
     * @return 记录成功返回true，失败返回false
     */
    bool LogRepayOperation(const RepayOperationRecord &record);
    
    /**
     * @brief 记录授权日志
     * @param record 授权记录
     * @return 记录成功返回true，失败返回false
     */
    bool LogAuthorization(const AuthorizationRecord &record);
    
    /**
     * @brief 查询补费操作记录
     * @param operatorId 操作员ID，为空则查询所有
     * @param startTime 开始时间，无效则不限制
     * @param endTime 结束时间，无效则不限制
     * @param limit 最大返回记录数，0表示不限制
     * @return 补费操作记录列表
     */
    QList<RepayOperationRecord> QueryRepayOperations(
        const QString &operatorId = QString(),
        const QDateTime &startTime = QDateTime(),
        const QDateTime &endTime = QDateTime(),
        int limit = 0);
    
    /**
     * @brief 查询授权记录
     * @param operatorId 操作员ID，为空则查询所有
     * @param startTime 开始时间，无效则不限制
     * @param endTime 结束时间，无效则不限制
     * @param limit 最大返回记录数，0表示不限制
     * @return 授权记录列表
     */
    QList<AuthorizationRecord> QueryAuthorizationLogs(
        const QString &operatorId = QString(),
        const QDateTime &startTime = QDateTime(),
        const QDateTime &endTime = QDateTime(),
        int limit = 0);
    
    /**
     * @brief 获取补费统计信息
     * @param operatorId 操作员ID，为空则统计所有
     * @param date 日期，无效则统计所有
     * @return 统计信息映射表
     */
    QMap<QString, QVariant> GetRepayStatistics(
        const QString &operatorId = QString(),
        const QDate &date = QDate());
    
    /**
     * @brief 清理过期日志
     * @param retentionDays 保留天数
     * @return 清理成功返回true，失败返回false
     */
    bool CleanupOldLogs(int retentionDays);
    
    /**
     * @brief 优化数据库
     * @return 优化成功返回true，失败返回false
     */
    bool OptimizeDatabase();
    
    /**
     * @brief 备份数据库
     * @param backupPath 备份文件路径，为空则使用默认路径
     * @return 备份成功返回true，失败返回false
     */
    bool BackupDatabase(const QString &backupPath = QString());
    
    /**
     * @brief 获取数据库状态信息
     * @return 状态信息映射表
     */
    QMap<QString, QVariant> GetDatabaseStatus();
    
    /**
     * @brief 检查数据库完整性
     * @return 完整性检查通过返回true，失败返回false
     */
    bool CheckDatabaseIntegrity();

signals:
    /**
     * @brief 数据库操作完成信号
     * @param operation 操作类型
     * @param success 是否成功
     * @param message 消息
     */
    void DatabaseOperationCompleted(const QString &operation, bool success, const QString &message);

private:
    explicit RepayDbManager(QObject *parent = 0);
    ~RepayDbManager();
    
    // 私有方法
    bool CreateTables();
    bool CreateIndexes();
    bool CreateViews();
    
private:
    static RepayDbManager *m_pInstance;
    static QMutex m_mutex;
    
    bool m_bInitialized;
    QString m_sDbPath;
    
    // 禁用拷贝构造和赋值操作
    RepayDbManager(const RepayDbManager &) = delete;
    RepayDbManager& operator=(const RepayDbManager &) = delete;
};

#endif // REPAY_DB_MANAGER_H