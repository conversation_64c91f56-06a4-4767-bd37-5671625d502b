#include "formdebtdetail.h"
#include "../../dlgmain.h"
#include "../../log4qt/ilogmsg.h"
#include <QApplication>
#include <QKeyEvent>
#include <QDateTime>

FormDebtDetail::FormDebtDetail(QWidget *parent)
    : CBaseOpWidget(parent)
    , m_pLblTitle(0)
    , m_pLblStatistics(0)
    , m_pLblStatus(0)
    , m_pLblHelp(0)
    , m_nCurrentIndex(0)
    , m_nScrollOffset(0)
    , m_nItemHeight(ITEM_HEIGHT)
    , m_nVisibleItems(0)
    , m_bAutoScroll(false)
    , m_bMultiSelect(true)
    , m_nTotalItems(0)
    , m_nSelectedItems(0)
    , m_nTotalAmount(0)
    , m_nSelectedAmount(0)
    , m_pScrollTimer(0)
    , m_pRefreshTimer(0)
{
    // 初始化定时器
    m_pScrollTimer = new QTimer(this);
    m_pRefreshTimer = new QTimer(this);
    
    // 初始化界面配置
    InitUIConfig();
    
    InfoLog("创建补费明细界面");
}

FormDebtDetail::~FormDebtDetail()
{
    InfoLog("销毁补费明细界面");
}

void FormDebtDetail::InitUIConfig()
{
    // 设置字体
    m_fontTitle = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TitleFontSize, QFont::Bold);
    m_fontText = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TextFontSize);
    m_fontItem = QFont(g_GlobalUI.m_FontName, 12);
    m_fontStatistics = QFont(g_GlobalUI.m_FontName, 14, QFont::Bold);
    
    // 设置颜色
    m_colorBackground = g_GlobalUI.m_ColorBackground;
    m_colorTitle = QColor(0, 0, 0);
    m_colorText = QColor(50, 50, 50);
    m_colorItem = QColor(248, 248, 248);
    m_colorSelectedItem = QColor(173, 216, 230);
    m_colorCurrentItem = QColor(255, 255, 200);
    m_colorBorder = QColor(180, 180, 180);
    m_colorScrollBar = QColor(200, 200, 200);
    m_colorError = QColor(220, 50, 50);
    m_colorSuccess = QColor(50, 150, 50);
    m_colorWarning = QColor(200, 120, 0);
}

void FormDebtDetail::InitUI(int iFlag)
{
    CBaseOpWidget::InitUI(iFlag);
    
    InfoLog("初始化补费明细界面");
    
    // 初始化控件
    InitControls();
    
    // 设置控件属性
    SetupControlProperties();
    
    // 设置布局
    InitLayout();
    
    // 连接信号
    InitConnections();
    
    DebugLog("补费明细界面初始化完成");
}

void FormDebtDetail::InitControls()
{
    // 创建所有控件
    m_pLblTitle = new QLabel(this);
    m_pLblStatistics = new QLabel(this);
    m_pLblStatus = new QLabel(this);
    m_pLblHelp = new QLabel(this);
}

void FormDebtDetail::SetupControlProperties()
{
    // 设置标题
    m_pLblTitle->setFont(m_fontTitle);
    m_pLblTitle->setAlignment(Qt::AlignCenter);
    m_pLblTitle->setText(QString::fromUtf8("省内欠费明细"));
    m_pLblTitle->setStyleSheet("color: rgb(0, 0, 0);");
    
    // 设置统计信息
    m_pLblStatistics->setFont(m_fontStatistics);
    m_pLblStatistics->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    m_pLblStatistics->setStyleSheet("color: rgb(0, 120, 215); background-color: rgb(240, 240, 240); padding: 5px;");
    
    // 设置状态信息
    m_pLblStatus->setFont(m_fontText);
    m_pLblStatus->setAlignment(Qt::AlignCenter);
    m_pLblStatus->setWordWrap(true);
    
    // 设置帮助信息
    m_pLblHelp->setFont(QFont(g_GlobalUI.m_FontName, 12));
    m_pLblHelp->setAlignment(Qt::AlignCenter);
    m_pLblHelp->setWordWrap(true);
    m_pLblHelp->setStyleSheet("color: rgb(100, 100, 100);");
    
    UpdateHelpMessage();
}

void FormDebtDetail::InitLayout()
{
    QRect rectClient = this->rect();
    int totalHeight = rectClient.height();
    int width = rectClient.width();
    int currentY = 0;
    
    // 标题区域
    m_pLblTitle->setGeometry(0, currentY, width, TITLE_HEIGHT);
    currentY += TITLE_HEIGHT;
    
    // 统计信息区域
    m_pLblStatistics->setGeometry(0, currentY, width, STATISTICS_HEIGHT);
    currentY += STATISTICS_HEIGHT;
    
    // 计算明细显示区域
    int availableHeight = totalHeight - currentY - STATUS_HEIGHT - HELP_HEIGHT;
    m_nVisibleItems = (availableHeight - 2 * MARGIN) / m_nItemHeight;
    
    currentY += availableHeight;
    
    // 状态信息区域
    m_pLblStatus->setGeometry(MARGIN, currentY, width - 2*MARGIN, STATUS_HEIGHT);
    currentY += STATUS_HEIGHT;
    
    // 帮助信息区域
    m_pLblHelp->setGeometry(MARGIN, currentY, width - 2*MARGIN, HELP_HEIGHT);
}

void FormDebtDetail::InitConnections()
{
    // 定时器信号连接
    connect(m_pScrollTimer, SIGNAL(timeout()), this, SLOT(OnScrollTimer()));
    connect(m_pRefreshTimer, SIGNAL(timeout()), this, SLOT(OnRefreshTimer()));
}

void FormDebtDetail::SetDebtResult(const RepayDebtQueryResult &result)
{
    m_debtResult = result;
    m_nTotalItems = result.debtItems.size();
    
    // 初始化选择状态
    m_itemSelections.clear();
    for (int i = 0; i < m_nTotalItems; i++) {
        m_itemSelections.append(false);
    }
    
    // 重置界面状态
    m_nCurrentIndex = 0;
    m_nScrollOffset = 0;
    
    // 更新统计信息
    UpdateStatistics();
    
    // 更新滚动范围
    UpdateScrollRange();
    
    InfoLog(QString("设置明细数据完成 - 总数:%1, 总金额:%2分")
            .arg(m_nTotalItems).arg(m_nTotalAmount));
}

bool FormDebtDetail::ShowDebtDetail(const RepayDebtQueryResult &result)
{
    InfoLog("显示补费明细界面");
    
    // 设置数据
    SetDebtResult(result);
    
    if (m_nTotalItems == 0) {
        ShowWarningMessage(QString::fromUtf8("未找到欠费明细"));
        return false;
    }
    
    // 显示界面
    int dialogResult = doModalShow();
    
    if (dialogResult == CBaseOpWidget::Rlt_OK) {
        InfoLog(QString("明细确认完成 - 选择:%1项, 金额:%2分")
                .arg(m_nSelectedItems).arg(m_nSelectedAmount));
        return true;
    } else {
        InfoLog("明细查看取消");
        return false;
    }
}

QList<RepayDebtItem> FormDebtDetail::GetSelectedItems() const
{
    QList<RepayDebtItem> selectedItems;
    
    for (int i = 0; i < m_nTotalItems; i++) {
        if (IsItemSelected(i)) {
            selectedItems.append(m_debtResult.debtItems[i]);
        }
    }
    
    return selectedItems;
}

int FormDebtDetail::GetTotalRepayAmount() const
{
    return m_nSelectedAmount;
}

void FormDebtDetail::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event);
    
    QRect rectClient = this->rect();
    QPixmap pixmap(rectClient.width(), rectClient.height());
    QPainter painter(&pixmap);
    
    // 绘制背景
    DrawBackground(painter);
    
    // 绘制统计信息
    DrawStatistics(painter);
    
    // 绘制明细列表
    DrawDebtListItems(painter);
    
    // 绘制滚动条
    DrawScrollBar(painter);
    
    // 绘制状态信息
    DrawStatusMessage(painter);
    
    // 绘制帮助信息
    DrawHelpMessage(painter);
    
    painter.end();
    
    // 绘制到窗口
    QPainter windowPainter(this);
    windowPainter.drawPixmap(rectClient, pixmap);
}

void FormDebtDetail::DrawBackground(QPainter &painter)
{
    QRect rectClient = this->rect();
    
    // 绘制背景色
    painter.setBrush(m_colorBackground);
    painter.setPen(Qt::black);
    painter.drawRect(rectClient.x(), rectClient.y(), rectClient.width() - 1, rectClient.height() - 1);
}

void FormDebtDetail::DrawStatistics(QPainter &painter)
{
    if (!m_pLblStatistics) return;
    
    QString statisticsText = QString::fromUtf8("共 %1 笔欠费，总金额：%2 元")
                           .arg(m_nTotalItems)
                           .arg(QString::number(m_nTotalAmount / 100.0, 'f', 2));
    
    if (m_nSelectedItems > 0) {
        statisticsText += QString::fromUtf8("  |  已选择 %1 笔，金额：%2 元")
                         .arg(m_nSelectedItems)
                         .arg(QString::number(m_nSelectedAmount / 100.0, 'f', 2));
    }
    
    m_pLblStatistics->setText(statisticsText);
}

void FormDebtDetail::DrawDebtListItems(QPainter &painter)
{
    if (m_nTotalItems == 0) {
        // 绘制无数据提示
        painter.setFont(m_fontItem);
        painter.setPen(m_colorText);
        
        QRect listRect = GetVisibleRect();
        painter.drawText(listRect, Qt::AlignCenter, QString::fromUtf8("暂无欠费明细数据"));
        return;
    }
    
    QRect listRect = GetVisibleRect();
    
    // 绘制列表背景
    painter.setBrush(Qt::white);
    painter.setPen(m_colorBorder);
    painter.drawRect(listRect);
    
    // 绘制明细项
    int startIndex = m_nScrollOffset;
    int endIndex = qMin(startIndex + m_nVisibleItems, m_nTotalItems);
    
    for (int i = startIndex; i < endIndex; i++) {
        QRect itemRect = GetItemRect(i);
        if (itemRect.isValid()) {
            bool isSelected = IsItemSelected(i);
            bool isCurrent = (i == m_nCurrentIndex);
            
            DrawDebtItem(painter, m_debtResult.debtItems[i], itemRect, isSelected || isCurrent, i);
        }
    }
}

void FormDebtDetail::DrawDebtItem(QPainter &painter, const RepayDebtItem &item, const QRect &itemRect, bool isSelected, int itemIndex)
{
    // 绘制项目背景
    if (isSelected) {
        painter.setBrush(m_colorSelectedItem);
    } else {
        painter.setBrush(m_colorItem);
    }
    painter.setPen(m_colorBorder);
    painter.drawRect(itemRect);
    
    // 设置文字颜色
    painter.setPen(Qt::black);
    painter.setFont(m_fontItem);
    
    // 计算文字位置
    int leftX = itemRect.left() + MIN_ITEM_MARGIN;
    int rightX = itemRect.right() - MIN_ITEM_MARGIN;
    int midX = (leftX + rightX) / 2;
    int topY = itemRect.top() + 6;
    int lineHeight = 18;
    
    // 格式化金额
    QString formattedAmount;
    FormatAmount(item.debtAmount, formattedAmount);
    
    // 格式化时间
    QString formattedEnTime, formattedExTime;
    FormatDateTime(item.debtDate, formattedEnTime);
    FormatDateTime(item.debtDate, formattedExTime);
    
    // 左侧信息
    painter.drawText(leftX, topY + lineHeight, QString::fromUtf8("入口：%1").arg(item.entryStation));
    painter.drawText(leftX, topY + lineHeight * 2, QString::fromUtf8("出口：%1").arg(item.exitStation));
    painter.drawText(leftX, topY + lineHeight * 3, QString::fromUtf8("欠费：%1").arg(formattedAmount));
    
    // 右侧信息
    painter.drawText(midX, topY + lineHeight, QString::fromUtf8("入口时间：%1").arg(formattedEnTime));
    painter.drawText(midX, topY + lineHeight * 2, QString::fromUtf8("出口时间：%1").arg(formattedExTime));
    painter.drawText(midX, topY + lineHeight * 3, QString::fromUtf8("工单号：%1").arg(item.orderIds));
    
    // 绘制选择标记
    if (IsItemSelected(itemIndex)) {
        QRect checkRect(itemRect.right() - 30, itemRect.top() + 5, 20, 20);
        painter.setBrush(m_colorSuccess);
        painter.setPen(Qt::white);
        painter.drawEllipse(checkRect);
        painter.drawText(checkRect, Qt::AlignCenter, "√");
    }
}

// 获取指定位置的明细项索引
int FormDebtDetail::GetItemAtPosition(const QPoint &pos) const
{
    QRect listRect = GetVisibleRect();
    if (!listRect.contains(pos)) {
        return -1;
    }
    
    int relativeY = pos.y() - listRect.top();
    int itemIndex = m_nScrollOffset + (relativeY / m_nItemHeight);
    
    if (itemIndex >= 0 && itemIndex < m_nTotalItems) {
        return itemIndex;
    }
    
    return -1;
}

// 更新滚动范围
void FormDebtDetail::UpdateScrollRange()
{
    // 计算可见明细项数量
    QRect visibleRect = GetVisibleRect();
    m_nVisibleItems = visibleRect.height() / m_nItemHeight;
    
    // 确保滚动偏移量在有效范围内
    CheckScrollBounds();
}

// 获取可见明细项数量
int FormDebtDetail::GetVisibleItemCount() const
{
    return m_nVisibleItems;
}

// 判断明细项是否可见
bool FormDebtDetail::IsItemVisible(int index) const
{
    return index >= m_nScrollOffset && index < m_nScrollOffset + m_nVisibleItems;
}

// 设置界面启用状态
void FormDebtDetail::SetUIEnabled(bool enabled)
{
    this->setEnabled(enabled);
}

// 更新明细项显示
void FormDebtDetail::UpdateItemsDisplay()
{
    update();
}

// 刷新显示
void FormDebtDetail::RefreshDisplay()
{
    UpdateStatistics();
    update();
}

// 清除状态信息
void FormDebtDetail::ClearStatusMessage()
{
    if (m_pLblStatus) {
        m_pLblStatus->clear();
    }
}

// 获取车辆类型文本
QString FormDebtDetail::GetVehicleTypeText(int vehType) const
{
    switch (vehType) {
        case 1: return QString::fromUtf8("客车");
        case 2: return QString::fromUtf8("货车");
        case 3: return QString::fromUtf8("专项作业车");
        default: return QString::fromUtf8("未知");
    }
}

// 获取车牌颜色文本
QString FormDebtDetail::GetPlateColorText(int plateColor) const
{
    switch (plateColor) {
        case 1: return QString::fromUtf8("蓝");
        case 2: return QString::fromUtf8("黄");
        case 3: return QString::fromUtf8("黑");
        case 4: return QString::fromUtf8("白");
        case 5: return QString::fromUtf8("绿");
        default: return QString::fromUtf8("其他");
    }
}

void FormDebtDetail::DrawScrollBar(QPainter &painter)
{
    if (m_nTotalItems <= m_nVisibleItems) {
        return; // 不需要滚动条
    }
    
    QRect listRect = GetVisibleRect();
    QRect scrollBarRect(listRect.right() - SCROLLBAR_WIDTH, listRect.top(), 
                       SCROLLBAR_WIDTH, listRect.height());
    
    // 绘制滚动条背景
    painter.setBrush(QColor(240, 240, 240));
    painter.setPen(m_colorBorder);
    painter.drawRect(scrollBarRect);
    
    // 计算滚动滑块位置和大小
    int totalScrollRange = m_nTotalItems - m_nVisibleItems;
    if (totalScrollRange > 0) {
        int thumbHeight = qMax(20, (m_nVisibleItems * scrollBarRect.height()) / m_nTotalItems);
        int thumbY = scrollBarRect.top() + (m_nScrollOffset * (scrollBarRect.height() - thumbHeight)) / totalScrollRange;
        
        QRect thumbRect(scrollBarRect.left() + 2, thumbY, scrollBarRect.width() - 4, thumbHeight);
        
        // 绘制滚动滑块
        painter.setBrush(m_colorScrollBar);
        painter.setPen(Qt::NoPen);
        painter.drawRect(thumbRect);
    }
}

void FormDebtDetail::DrawStatusMessage(QPainter &painter)
{
    // 状态信息由QLabel绘制，这里不需要额外绘制
}

void FormDebtDetail::DrawHelpMessage(QPainter &painter)
{
    // 帮助信息由QLabel绘制，这里不需要额外绘制
}

int FormDebtDetail::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    if (!mtcKeyEvent) return 0;
    
    int keyCode = mtcKeyEvent->key();
    
    switch (keyCode) {
        case KeyUp:
            ProcessUpKey();
            break;
        case KeyDown:
            ProcessDownKey();
            break;
        case KeyShift:  // PageUp键映射到KeyShift
            ProcessPageUpKey();
            break;
        case KeyAlarm:  // PageDown键映射到KeyAlarm
            ProcessPageDownKey();
            break;
        case KeyTicket:  // Home键映射到KeyTicket
            ProcessHomeKey();
            break;
        case KeyFunc:  // End键映射到KeyFunc
            ProcessEndKey();
            break;
        case KeyPoint:  // 使用点号作为空格键功能
            ProcessSpaceKey();
            break;
        case KeyConfirm:
            ProcessEnterKey();
            break;
        case KeyEsc:
            ProcessEscapeKey();
            break;
        case KeyCar1:
            ProcessF1Key();
            break;
        case KeyCar2:
            ProcessF2Key();
            break;
        case KeyCar3:
            ProcessF3Key();
            break;
        default:
            return CBaseOpWidget::mtcKeyPressed(mtcKeyEvent);
    }
    
    return 1;
}

void FormDebtDetail::OnModalShowed()
{
    CBaseOpWidget::OnModalShowed();
    
    // 开始自动刷新定时器
    if (m_pRefreshTimer) {
        m_pRefreshTimer->start(REFRESH_TIMER_INTERVAL);
    }
    
    DebugLog("补费明细界面显示完成");
}

void FormDebtDetail::ProcessUpKey()
{
    if (m_nTotalItems == 0) return;
    
    MoveSelectionUp();
    update();
}

void FormDebtDetail::ProcessDownKey()
{
    if (m_nTotalItems == 0) return;
    
    MoveSelectionDown();
    update();
}

void FormDebtDetail::ProcessPageUpKey()
{
    if (m_nTotalItems == 0) return;
    
    ScrollPageUp();
    update();
}

void FormDebtDetail::ProcessPageDownKey()
{
    if (m_nTotalItems == 0) return;
    
    ScrollPageDown();
    update();
}

void FormDebtDetail::ProcessHomeKey()
{
    if (m_nTotalItems == 0) return;
    
    ScrollToTop();
    update();
}

void FormDebtDetail::ProcessEndKey()
{
    if (m_nTotalItems == 0) return;
    
    ScrollToBottom();
    update();
}

void FormDebtDetail::ProcessSpaceKey()
{
    if (m_nTotalItems == 0) return;
    
    ToggleItemSelection(m_nCurrentIndex);
    UpdateStatistics();
    update();
}

void FormDebtDetail::ProcessEnterKey()
{
    if (m_nSelectedItems > 0) {
        InfoLog(QString("确认补费选择 - %1项, %2分").arg(m_nSelectedItems).arg(m_nSelectedAmount));
        OnOk();
    } else {
        ShowWarningMessage(QString::fromUtf8("请先选择要补费的明细项"));
    }
}

void FormDebtDetail::ProcessEscapeKey()
{
    InfoLog("取消明细查看");
    OnCancel();
}

void FormDebtDetail::ProcessF1Key()
{
    if (m_nTotalItems == 0) return;
    
    SelectAllItems();
    ShowSuccessMessage(QString::fromUtf8("已选择全部明细"));
    update();
}

void FormDebtDetail::ProcessF2Key()
{
    if (m_nTotalItems == 0) return;
    
    ClearAllSelections();
    ShowWarningMessage(QString::fromUtf8("已清除所有选择"));
    update();
}

void FormDebtDetail::ProcessF3Key()
{
    ProcessEnterKey();
}

void FormDebtDetail::ScrollUp()
{
    if (m_nScrollOffset > 0) {
        m_nScrollOffset--;
        CheckScrollBounds();
    }
}

void FormDebtDetail::ScrollDown()
{
    int maxOffset = qMax(0, m_nTotalItems - m_nVisibleItems);
    if (m_nScrollOffset < maxOffset) {
        m_nScrollOffset++;
        CheckScrollBounds();
    }
}

void FormDebtDetail::ScrollPageUp()
{
    m_nScrollOffset = qMax(0, m_nScrollOffset - SCROLL_PAGE);
    CheckScrollBounds();
}

void FormDebtDetail::ScrollPageDown()
{
    int maxOffset = qMax(0, m_nTotalItems - m_nVisibleItems);
    m_nScrollOffset = qMin(maxOffset, m_nScrollOffset + SCROLL_PAGE);
    CheckScrollBounds();
}

void FormDebtDetail::ScrollToTop()
{
    m_nScrollOffset = 0;
    m_nCurrentIndex = 0;
    CheckScrollBounds();
}

void FormDebtDetail::ScrollToBottom()
{
    int maxOffset = qMax(0, m_nTotalItems - m_nVisibleItems);
    m_nScrollOffset = maxOffset;
    m_nCurrentIndex = m_nTotalItems - 1;
    CheckScrollBounds();
}

void FormDebtDetail::ScrollToItem(int index)
{
    if (index < 0 || index >= m_nTotalItems) return;
    
    if (index < m_nScrollOffset) {
        m_nScrollOffset = index;
    } else if (index >= m_nScrollOffset + m_nVisibleItems) {
        m_nScrollOffset = index - m_nVisibleItems + 1;
    }
    
    m_nCurrentIndex = index;
    CheckScrollBounds();
}

void FormDebtDetail::CheckScrollBounds()
{
    // 检查滚动偏移量边界
    int maxOffset = qMax(0, m_nTotalItems - m_nVisibleItems);
    m_nScrollOffset = qBound(0, m_nScrollOffset, maxOffset);
    
    // 检查当前选择项边界
    m_nCurrentIndex = qBound(0, m_nCurrentIndex, m_nTotalItems - 1);
}

void FormDebtDetail::MoveSelectionUp()
{
    if (m_nCurrentIndex > 0) {
        m_nCurrentIndex--;
        ScrollToItem(m_nCurrentIndex);
    }
}

void FormDebtDetail::MoveSelectionDown()
{
    if (m_nCurrentIndex < m_nTotalItems - 1) {
        m_nCurrentIndex++;
        ScrollToItem(m_nCurrentIndex);
    }
}

void FormDebtDetail::SelectAllItems()
{
    for (int i = 0; i < m_nTotalItems; i++) {
        m_itemSelections[i] = true;
    }
    UpdateStatistics();
}

void FormDebtDetail::ClearAllSelections()
{
    for (int i = 0; i < m_nTotalItems; i++) {
        m_itemSelections[i] = false;
    }
    UpdateStatistics();
}

void FormDebtDetail::ToggleItemSelection(int index)
{
    if (index >= 0 && index < m_nTotalItems) {
        m_itemSelections[index] = !m_itemSelections[index];
        UpdateStatistics();
        
        QString action = m_itemSelections[index] ? QString::fromUtf8("选择") : QString::fromUtf8("取消选择");
        DebugLog(QString("%1明细项 - 索引:%2").arg(action).arg(index));
    }
}

void FormDebtDetail::UpdateStatistics()
{
    m_nSelectedItems = 0;
    m_nSelectedAmount = 0;
    m_nTotalAmount = 0;
    
    for (int i = 0; i < m_nTotalItems; i++) {
        int amount = m_debtResult.debtItems[i].debtAmount;
        m_nTotalAmount += amount;
        
        if (m_itemSelections[i]) {
            m_nSelectedItems++;
            m_nSelectedAmount += amount;
        }
    }
    
    // 更新统计显示
    update();
}

void FormDebtDetail::UpdateHelpMessage()
{
    QString helpText = QString::fromUtf8("↑↓ 选择  Space 选中/取消  Enter 确认  ESC 取消\nF1 全选  F2 清除  F3 确认补费  PageUp/PageDown 翻页");
    
    if (m_pLblHelp) {
        m_pLblHelp->setText(helpText);
    }
}

void FormDebtDetail::FormatAmount(int amount, QString &formatted) const
{
    double amountValue = amount / 100.0;
    formatted = QString::number(amountValue, 'f', AMOUNT_DECIMAL_PLACES) + "元";
}

void FormDebtDetail::FormatDateTime(const QString &datetime, QString &formatted) const
{
    // 简化时间显示，只显示月日时分
    if (datetime.length() >= 16) {
        formatted = datetime.mid(5, 11); // MM-DD HH:MM
    } else {
        formatted = datetime;
    }
}

QRect FormDebtDetail::GetItemRect(int index) const
{
    QRect listRect = GetVisibleRect();
    int relativeIndex = index - m_nScrollOffset;
    
    if (relativeIndex >= 0 && relativeIndex < m_nVisibleItems) {
        int itemY = listRect.top() + relativeIndex * m_nItemHeight;
        return QRect(listRect.left(), itemY, listRect.width() - SCROLLBAR_WIDTH, m_nItemHeight);
    }
    
    return QRect(); // 无效矩形
}

QRect FormDebtDetail::GetVisibleRect() const
{
    QRect rectClient = this->rect();
    int listTop = TITLE_HEIGHT + STATISTICS_HEIGHT + MARGIN;
    int listHeight = rectClient.height() - listTop - STATUS_HEIGHT - HELP_HEIGHT - MARGIN;
    
    return QRect(MARGIN, listTop, rectClient.width() - 2 * MARGIN, listHeight);
}

bool FormDebtDetail::IsItemSelected(int index) const
{
    return index >= 0 && index < m_itemSelections.size() && m_itemSelections[index];
}

void FormDebtDetail::ShowErrorMessage(const QString &message)
{
    if (m_pLblStatus) {
        m_pLblStatus->setText(message);
        m_pLblStatus->setStyleSheet("color: rgb(220, 50, 50);");
    }
    ErrorLog(QString("补费明细界面错误：%1").arg(message));
}

void FormDebtDetail::ShowSuccessMessage(const QString &message)
{
    if (m_pLblStatus) {
        m_pLblStatus->setText(message);
        m_pLblStatus->setStyleSheet("color: rgb(50, 150, 50);");
    }
    InfoLog(QString("补费明细界面成功：%1").arg(message));
}

void FormDebtDetail::ShowWarningMessage(const QString &message)
{
    if (m_pLblStatus) {
        m_pLblStatus->setText(message);
        m_pLblStatus->setStyleSheet("color: rgb(200, 120, 0);");
    }
    WarnLog(QString("补费明细界面警告：%1").arg(message));
}

void FormDebtDetail::OnScrollTimer()
{
    // 滚动定时器处理（如果需要自动滚动）
    if (m_bAutoScroll) {
        // 实现自动滚动逻辑
    }
}

void FormDebtDetail::OnRefreshTimer()
{
    // 自动刷新定时器处理
    // 可以在这里定期更新界面显示
    update();
}