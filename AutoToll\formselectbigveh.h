#ifndef FORMSELECTBIGVEH_H
#define FORMSELECTBIGVEH_H

#include <QtGui>
#include <QTableWidget>
#include <QHeaderView>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QLabel>

#include "baseopwidget.h"
#include "cbigvehlist.h"

/**
 * @brief 大件车选择对话框类
 * 
 * 当查询到多个大件车记录时，显示列表供操作员选择
 * 支持键盘导航和鼠标操作
 */
class FormSelectBigVeh : public CBaseOpWidget
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口指针
     */
    explicit FormSelectBigVeh(QWidget *parent = 0);
    
    /**
     * @brief 析构函数
     */
    ~FormSelectBigVeh();

    /**
     * @brief 显示大件车选择对话框
     * @param bigVehList 大件车信息列表
     * @param selectedInfo 输出参数，用户选择的大件车信息
     * @return true-用户确认选择，false-用户取消
     */
    bool ShowSelectDialog(const QList<CBigVehInfo> &bigVehList, CBigVehInfo &selectedInfo);

protected:
    /**
     * @brief 初始化用户界面
     */
    virtual void InitUI(int iFlag = 0);

    /**
     * @brief 处理键盘按键事件
     * @param mtcKeyEvent 按键事件对象
     * @return 1-处理成功，0-处理失败
     */
    virtual int mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent);

private slots:
    /**
     * @brief 确认按钮点击事件
     */
    void OnConfirm();
    
    /**
     * @brief 取消按钮点击事件
     */
    void OnCancel();
    
    /**
     * @brief 表格行选择变化事件
     * @param currentRow 当前选中行
     * @param currentColumn 当前选中列
     * @param previousRow 之前选中行
     * @param previousColumn 之前选中列
     */
    void OnTableCurrentChanged(int currentRow, int currentColumn, int previousRow, int previousColumn);

private:
    /**
     * @brief 设置表格数据
     * @param bigVehList 大件车信息列表
     */
    void SetTableData(const QList<CBigVehInfo> &bigVehList);
    
    /**
     * @brief 获取当前选中的大件车信息
     * @return 选中的大件车信息，如果未选中则返回空信息
     */
    CBigVehInfo GetSelectedBigVehInfo();
    
    /**
     * @brief 设置表格样式
     */
    void SetTableStyle();
    
    /**
     * @brief 格式化时间显示
     * @param timestamp 时间戳
     * @return 格式化后的时间字符串
     */
    QString FormatDateTime(quint32 timestamp);

private:
    QTableWidget *m_pTableWidget;       // 大件车信息表格
    QPushButton *m_pBtnConfirm;         // 确认按钮
    QPushButton *m_pBtnCancel;          // 取消按钮
    QLabel *m_pLblTitle;                // 标题标签
    QLabel *m_pLblInstruction;          // 操作说明标签
    
    QList<CBigVehInfo> m_bigVehList;    // 大件车信息列表
    int m_nSelectedIndex;               // 当前选中索引
    bool m_bConfirmed;                  // 是否确认选择
};

#endif // FORMSELECTBIGVEH_H 