#ifndef FORMREPAYSUCCESS_H
#define FORMREPAYSUCCESS_H

#include <QWidget>
#include <QLabel>
#include <QTimer>
#include "baseopwidget.h"
#include "../common/repaytypes.h"

/**
 * @brief 补费成功确认界面
 * 显示补费完成后的成功信息，包括车牌、金额、支付方式等
 */
class FormRepaySuccess : public CBaseOpWidget
{
    Q_OBJECT

public:
    explicit FormRepaySuccess(QWidget *parent = 0);
    ~FormRepaySuccess();

    /**
     * @brief 显示补费成功确认界面
     * @param repayTypeName 补费类型名称
     * @param vehPlate 车牌号
     * @param vehPlateColor 车牌颜色
     * @param payTypeName 支付方式名称
     * @param amount 金额（分）
     * @param autoCloseSeconds 自动关闭秒数
     * @return true-用户确认，false-用户取消或超时
     */
    bool ShowRepaySuccess(const QString &repayTypeName, 
                         const QString &vehPlate, 
                         int vehPlateColor,
                         const QString &payTypeName, 
                         int amount,
                         int autoCloseSeconds);

protected:
    // 界面初始化
    void InitUI();
    void CreateControls();
    void SetupControlProperties();
    void InitLayout();
    void InitConnections();

    // 事件处理
    int mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent);
    void paintEvent(QPaintEvent *event);

private slots:
    void OnConfirmClicked();
    void OnAutoClose();
    void OnCountdown();

private:
    // 辅助方法
    QString GetVehPlateColorName(int color);
    void UpdateCountdownDisplay();

private:
    // 界面控件
    QLabel *m_pLblTitle;           // 标题
    QLabel *m_pLblVehInfo;         // 车辆信息
    QLabel *m_pLblPayResult;       // 支付结果
    QLabel *m_pLblHelpInfo;        // 帮助信息

    // 定时器
    QTimer *m_pAutoCloseTimer;     // 自动关闭定时器
    QTimer *m_pCountdownTimer;     // 倒计时更新定时器

    // 数据
    QString m_repayTypeName;       // 补费类型名称
    QString m_vehPlate;            // 车牌号
    int m_vehPlateColor;           // 车牌颜色
    QString m_payTypeName;         // 支付方式名称
    int m_amount;                  // 金额（分）
    int m_autoCloseSeconds;        // 自动关闭秒数
    int m_remainingSeconds;        // 剩余秒数

    // 样式
    QFont m_fontTitle;
    QFont m_fontVehInfo;
    QFont m_fontContent;
    QFont m_fontHelp;
    QColor m_colorBackground;
};

#endif // FORMREPAYSUCCESS_H
