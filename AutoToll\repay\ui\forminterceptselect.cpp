#include "forminterceptselect.h"
#include "../../dlgmain.h"
#include "../../log4qt/ilogmsg.h"
#include <QApplication>
#include <QKeyEvent>

// 静态常量定义
const FormInterceptSelect::InterceptOption FormInterceptSelect::INTERCEPT_OPTIONS[OPTION_COUNT] = {
    { Intercept_Entry, QString::fromUtf8("入口拦截"), QString::fromUtf8("在入口收费站进行车辆拦截处理"), "1" },
    { Intercept_Exit,  QString::fromUtf8("出口拦截"), QString::fromUtf8("在出口收费站进行车辆拦截处理"), "2" }
};

FormInterceptSelect::FormInterceptSelect(QWidget *parent)
    : CBaseOpWidget(parent)
    , m_pLblTitle(0)
    , m_pLblOption1(0)
    , m_pLblOption2(0)
    
    , m_pLblStatus(0)
    , m_pLblHelp(0)
    , m_selectedType(Intercept_Exit)
    , m_nCurrentIndex(1)
    , m_bSelectionConfirmed(false)
    
    , m_bProcessing(false)
    , m_nSelectionTimeout(SELECTION_TIMEOUT_SECONDS)
    , m_pSelectionTimer(0)
{
    // 初始化定时器
    m_pSelectionTimer = new QTimer(this);
    
    // 初始化界面配置
    InitUIConfig();
    
    InfoLog("创建拦截方式选择界面");
}

FormInterceptSelect::~FormInterceptSelect()
{
    InfoLog("销毁拦截方式选择界面");
}

void FormInterceptSelect::InitUIConfig()
{
    // 设置字体
    m_fontTitle = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TitleFontSize, QFont::Bold);
    m_fontText = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TextFontSize);
    m_fontOption = QFont(g_GlobalUI.m_FontName, 16, QFont::Bold);
    m_fontDescription = QFont(g_GlobalUI.m_FontName, 12);
    
    // 设置颜色
    m_colorBackground = g_GlobalUI.m_ColorBackground;
    m_colorTitle = QColor(0, 0, 0);
    m_colorText = QColor(50, 50, 50);
    m_colorOption = QColor(0, 0, 0);
    m_colorSelectedOption = QColor(0, 120, 215);
    m_colorDescription = QColor(100, 100, 100);
    m_colorBorder = QColor(180, 180, 180);
    m_colorError = QColor(220, 50, 50);
    m_colorSuccess = QColor(50, 150, 50);
    m_colorWarning = QColor(200, 120, 0);
}

void FormInterceptSelect::InitUI(int iFlag)
{
    CBaseOpWidget::InitUI(iFlag);
    
    InfoLog("初始化拦截方式选择界面");
    
    // 初始化控件
    InitControls();
    
    // 设置控件属性
    SetupControlProperties();
    
    // 设置布局
    InitLayout();
    
    // 连接信号
    InitConnections();
    
    // 更新帮助信息
    UpdateHelpMessage();
    
    DebugLog("拦截方式选择界面初始化完成");
}

void FormInterceptSelect::InitControls()
{
    // 创建所有控件
    m_pLblTitle = new QLabel(this);
    m_pLblOption1 = new QLabel(this);
    m_pLblOption2 = new QLabel(this);
    
    m_pLblStatus = new QLabel(this);
    m_pLblHelp = new QLabel(this);
}

void FormInterceptSelect::SetupControlProperties()
{
    // 设置标题
    m_pLblTitle->setFont(m_fontTitle);
    m_pLblTitle->setAlignment(Qt::AlignCenter);
    m_pLblTitle->setText(QString::fromUtf8("拦截方式选择"));
    m_pLblTitle->setStyleSheet("color: rgb(0, 0, 0);");
    
    
    
    // 设置选项1
    m_pLblOption1->setFont(m_fontOption);
    m_pLblOption1->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    m_pLblOption1->setText(QString("1. %1").arg(INTERCEPT_OPTIONS[0].name));
    m_pLblOption1->setStyleSheet("color: rgb(0,0,0); background-color: transparent; padding: 6px 12px; border-radius: 6px;");
    
    // 选项1描述控件已移除
    
    // 设置选项2
    m_pLblOption2->setFont(m_fontOption);
    m_pLblOption2->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    m_pLblOption2->setText(QString("2. %1").arg(INTERCEPT_OPTIONS[1].name));
    m_pLblOption2->setStyleSheet("color: rgb(0,0,0); background-color: transparent; padding: 6px 12px; border-radius: 6px;");

    
    // 选项2描述控件已移除
    
    // 设置状态信息
    m_pLblStatus->setFont(m_fontText);
    m_pLblStatus->setAlignment(Qt::AlignCenter);
    m_pLblStatus->setWordWrap(true);
    
    // 设置帮助信息
    m_pLblHelp->setFont(m_fontText);
    m_pLblHelp->setAlignment(Qt::AlignCenter);
    m_pLblHelp->setWordWrap(true);
    m_pLblHelp->setStyleSheet("color: rgb(100, 100, 100);");
}

void FormInterceptSelect::InitLayout()
{
    // 采用与 FormPaymentSelect 相同的方式：使用布局实现水平/垂直居中
    QVBoxLayout *v = new QVBoxLayout();
    v->setContentsMargins(MARGIN, MARGIN, MARGIN, MARGIN);
    v->setSpacing(20);

    // 标题置中
    v->addWidget(m_pLblTitle, 0, Qt::AlignHCenter);

    // 中部选项区域（每项使用 HBox 双侧 addStretch 居中）
    v->addStretch();
    m_pOptionsLayout = new QVBoxLayout();
    m_pOptionsLayout->setContentsMargins(0, 0, 0, 0);
    m_pOptionsLayout->setSpacing(10);

    // 选项 1
    {
        QHBoxLayout *h1 = new QHBoxLayout();
        h1->setContentsMargins(0, 0, 0, 0);
        h1->addStretch();
        h1->addWidget(m_pLblOption1);
        h1->addStretch();
        m_pOptionsLayout->addLayout(h1);
    }

    // 选项 2
    {
        QHBoxLayout *h2 = new QHBoxLayout();
        h2->setContentsMargins(0, 0, 0, 0);
        h2->addStretch();
        h2->addWidget(m_pLblOption2);
        h2->addStretch();
        m_pOptionsLayout->addLayout(h2);
    }

    v->addLayout(m_pOptionsLayout);
    v->addStretch();

    // 状态与帮助置中
    v->addWidget(m_pLblStatus, 0, Qt::AlignHCenter);
    v->addWidget(m_pLblHelp, 0, Qt::AlignHCenter);

    setLayout(v);
    m_pMainLayout = v;
}

void FormInterceptSelect::InitConnections()
{
    // 定时器信号连接
    connect(m_pSelectionTimer, SIGNAL(timeout()), this, SLOT(OnSelectionTimeout()));
}

bool FormInterceptSelect::ShowInterceptSelect(InterceptType &selectedType)
{
    InfoLog("显示拦截方式选择界面");
    
    // 重置状态
    m_bSelectionConfirmed = false;
    m_bProcessing = false;
    m_selectedType = Intercept_Exit;  // 默认选择出口拦截
    m_nCurrentIndex = 1;
    
    // 更新界面显示
    UpdateSelectionDisplay();
    
    // 启动选择超时定时器
    StartSelectionTimer();
    
    // 显示界面
    int result = doModalShow();
    
    // 停止定时器
    StopSelectionTimer();
    
    if (result == CBaseOpWidget::Rlt_OK && m_bSelectionConfirmed) {
        selectedType = m_selectedType;
        InfoLog(QString("拦截方式选择完成 - 类型:%1(%2)")
                .arg(static_cast<int>(selectedType))
                .arg(GetInterceptTypeName(selectedType)));
        return true;
    } else {
        InfoLog("拦截方式选择取消");
        return false;
    }
}

void FormInterceptSelect::SetDefaultSelection(InterceptType defaultType)
{
    m_selectedType = defaultType;
    m_nCurrentIndex = GetIndexByInterceptType(defaultType);
    UpdateSelectionDisplay();
}

// 提示信息接口已移除

void FormInterceptSelect::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event);
    
    QRect rectClient = this->rect();
    QPixmap pixmap(rectClient.width(), rectClient.height());
    QPainter painter(&pixmap);
    
    // 绘制背景
    DrawBackground(painter);
    
    // 绘制选择指示器
    DrawSelectionIndicator(painter);
    
    painter.end();
    
    // 绘制到窗口
    QPainter windowPainter(this);
    windowPainter.drawPixmap(rectClient, pixmap);
}

void FormInterceptSelect::DrawBackground(QPainter &painter)
{
    QRect rectClient = this->rect();
    
    // 绘制背景色
    painter.setBrush(m_colorBackground);
    painter.setPen(Qt::black);
    painter.drawRect(rectClient.x(), rectClient.y(), rectClient.width() - 1, rectClient.height() - 1);
}

void FormInterceptSelect::DrawSelectionIndicator(QPainter &painter)
{
    // 选择指示器由QLabel的样式表现，这里不需要额外绘制
}

int FormInterceptSelect::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    if (!mtcKeyEvent || m_bProcessing) return 0;
    
    // 按项目规范：func() 表示功能键值；key() 表示普通键值
    int keyCode = mtcKeyEvent->func();
    
    switch (keyCode) {
        case Key1:
            ProcessKey1();
            break;
        case Key2:
            ProcessKey2();
            break;
        case KeyUp:
            ProcessUpKey();
            break;
        case KeyDown:
            ProcessDownKey();
            break;
        case KeyConfirm:
            ProcessEnterKey();
            break;
        case KeyEsc:
            ProcessEscapeKey();
            break;
        default:
            return CBaseOpWidget::mtcKeyPressed(mtcKeyEvent);
    }
    
    return 1;
}

void FormInterceptSelect::OnModalShowed()
{
    CBaseOpWidget::OnModalShowed();
    
    // 设置焦点
    this->setFocus();
    
    DebugLog("拦截方式选择界面显示完成");
}

void FormInterceptSelect::ProcessKey1()
{
    SetCurrentSelection(0);
    DebugLog("用户按键选择：入口拦截");
}

void FormInterceptSelect::ProcessKey2()
{
    SetCurrentSelection(1);
    DebugLog("用户按键选择：出口拦截");
}

void FormInterceptSelect::ProcessUpKey()
{
    int newIndex = (m_nCurrentIndex > 0) ? m_nCurrentIndex - 1 : OPTION_COUNT - 1;
    SetCurrentSelection(newIndex);
    DebugLog(QString("用户上键选择：索引%1").arg(newIndex));
}

void FormInterceptSelect::ProcessDownKey()
{
    int newIndex = (m_nCurrentIndex < OPTION_COUNT - 1) ? m_nCurrentIndex + 1 : 0;
    SetCurrentSelection(newIndex);
    DebugLog(QString("用户下键选择：索引%1").arg(newIndex));
}

void FormInterceptSelect::ProcessEnterKey()
{
    ConfirmSelection();
}

void FormInterceptSelect::ProcessEscapeKey()
{
    CancelSelection();
}

void FormInterceptSelect::SetCurrentSelection(int index)
{
    if (index >= 0 && index < OPTION_COUNT) {
        m_nCurrentIndex = index;
        m_selectedType = INTERCEPT_OPTIONS[index].type;
        UpdateSelectionDisplay();
        
        // 重启选择定时器
        StartSelectionTimer();
    }
}

void FormInterceptSelect::ConfirmSelection()
{
    if (m_bProcessing) return;
    
    m_bSelectionConfirmed = true;
    
    InfoLog(QString("确认选择拦截方式 - %1").arg(GetInterceptTypeName(m_selectedType)));
    
    ShowSuccessMessage(QString::fromUtf8("已选择：%1").arg(GetInterceptTypeName(m_selectedType)));
    
    // 延迟500ms后关闭界面
    //QTimer::singleShot(500, this, SLOT(OnDelayedOk()));
    OnOk();
}

void FormInterceptSelect::CancelSelection()
{
    InfoLog("用户取消拦截方式选择");
    OnCancel();
}

void FormInterceptSelect::UpdateSelectionDisplay()
{
    if (!m_pLblOption1 || !m_pLblOption2) return;
    
    // 与 FormPaymentSelect 一致的高亮样式
    QString normal   = "color: rgb(0,0,0); background-color: transparent; padding: 6px 12px; border-radius: 6px;";
    QString selected = "color: rgb(255,255,255); background-color: rgb(0,120,215); padding: 6px 12px; border-radius: 6px;";
    if (m_nCurrentIndex == 0) {
        m_pLblOption1->setStyleSheet(selected);
        m_pLblOption2->setStyleSheet(normal);
    } else {
        m_pLblOption1->setStyleSheet(normal);
        m_pLblOption2->setStyleSheet(selected);
    }
    
    // 清除状态信息
    ClearStatusMessage();
    
    update();
}

void FormInterceptSelect::StartSelectionTimer()
{
    if (m_pSelectionTimer) {
        m_pSelectionTimer->stop();
        m_pSelectionTimer->start(m_nSelectionTimeout * 1000);
    }
}

void FormInterceptSelect::StopSelectionTimer()
{
    if (m_pSelectionTimer) {
        m_pSelectionTimer->stop();
    }
}

void FormInterceptSelect::UpdateHelpMessage()
{
    QString helpText = QString::fromUtf8("按 【1】【2】 数字键选择  按 【↑】【↓】 移动选择\n按 【Enter】 确认选择  按 【ESC】 取消选择");
    
    if (m_pLblHelp) {
        m_pLblHelp->setText(helpText);
    }
}

void FormInterceptSelect::OnSelectionTimeout()
{
    WarnLog("拦截方式选择超时");
    ShowWarningMessage(QString::fromUtf8("选择超时，将使用默认设置"));
    
    // 使用当前选择作为默认选择
    QTimer::singleShot(2000, this, SLOT(OnDelayedConfirmSelection()));
}

void FormInterceptSelect::SetUIEnabled(bool enabled)
{
    this->setEnabled(enabled);
}

void FormInterceptSelect::ShowErrorMessage(const QString &message)
{
    if (m_pLblStatus) {
        m_pLblStatus->setText(message);
        m_pLblStatus->setStyleSheet("color: rgb(220, 50, 50);");
    }
    ErrorLog(QString("拦截方式选择界面错误：%1").arg(message));
}

void FormInterceptSelect::ShowSuccessMessage(const QString &message)
{
    if (m_pLblStatus) {
        m_pLblStatus->setText(message);
        m_pLblStatus->setStyleSheet("color: rgb(50, 150, 50);");
    }
    InfoLog(QString("拦截方式选择界面成功：%1").arg(message));
}

void FormInterceptSelect::ShowWarningMessage(const QString &message)
{
    if (m_pLblStatus) {
        m_pLblStatus->setText(message);
        m_pLblStatus->setStyleSheet("color: rgb(200, 120, 0);");
    }
    WarnLog(QString("拦截方式选择界面警告：%1").arg(message));
}

void FormInterceptSelect::ClearStatusMessage()
{
    if (m_pLblStatus) {
        m_pLblStatus->clear();
    }
}

InterceptType FormInterceptSelect::GetInterceptTypeByIndex(int index) const
{
    if (index >= 0 && index < OPTION_COUNT) {
        return INTERCEPT_OPTIONS[index].type;
    }
    return Intercept_Exit; // 默认返回出口拦截
}

int FormInterceptSelect::GetIndexByInterceptType(InterceptType type) const
{
    for (int i = 0; i < OPTION_COUNT; i++) {
        if (INTERCEPT_OPTIONS[i].type == type) {
            return i;
        }
    }
    return 1; // 默认返回出口拦截的索引
}

QString FormInterceptSelect::GetInterceptTypeName(InterceptType type) const
{
    for (int i = 0; i < OPTION_COUNT; i++) {
        if (INTERCEPT_OPTIONS[i].type == type) {
            return INTERCEPT_OPTIONS[i].name;
        }
    }
    return QString::fromUtf8("出口拦截"); // 默认名称
}

QString FormInterceptSelect::GetInterceptTypeDescription(InterceptType type) const
{
    for (int i = 0; i < OPTION_COUNT; i++) {
        if (INTERCEPT_OPTIONS[i].type == type) {
            return INTERCEPT_OPTIONS[i].description;
        }
    }
    return QString::fromUtf8("在出口收费站进行车辆拦截处理"); // 默认描述
}

// 延时操作slot实现
void FormInterceptSelect::OnDelayedOk()
{
    OnOk();
}

void FormInterceptSelect::OnDelayedConfirmSelection()
{
    ConfirmSelection();
}
