#ifndef FORMREPAYCOMPLETE_H
#define FORMREPAYCOMPLETE_H

#include <QWidget>
#include <QLabel>
#include <QTimer>
#include "baseopwidget.h"
#include "../common/repaytypes.h"

/**
 * @brief 补费完成界面
 * 显示补费成功信息和相关详情
 */
class FormRepayComplete : public CBaseOpWidget
{
    Q_OBJECT

public:
    explicit FormRepayComplete(QWidget *parent = 0);
    ~FormRepayComplete();

    /**
     * @brief 显示补费完成界面
     * @param vehPlate 车牌号
     * @param vehType 车型
     * @param payType 支付方式
     * @param amount 补费金额（分）
     * @param repayType 补费类型
     * @return true-用户确认，false-超时自动关闭
     */
    bool ShowRepayComplete(const QString &vehPlate, int vehType, CTransPayType payType, 
                          int amount, RepayType repayType);

protected:
    // 界面初始化
    void InitUI();
    void CreateControls();
    void SetupControlProperties();
    void InitLayout();
    void InitConnections();

    // 事件处理
    int mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent);
    void paintEvent(QPaintEvent *event);

private slots:
    void OnConfirmClicked();
    void OnAutoClose();

private:
    // 辅助方法
    QString GetPayTypeName(CTransPayType payType);
    QString GetVehTypeName(int vehType);
    QString GetRepayTypeName(RepayType repayType);

private:
    // 界面控件
    QLabel *m_pLblTitle;           // 标题（补费完成）
    QLabel *m_pLblSuccessIcon;     // 成功图标
    QLabel *m_pLblVehPlate;        // 车牌信息
    QLabel *m_pLblVehType;         // 车型信息
    QLabel *m_pLblPayType;         // 支付方式
    QLabel *m_pLblAmount;          // 补费金额
    QLabel *m_pLblRepayType;       // 补费类型
    QLabel *m_pLblDateTime;        // 完成时间
    QLabel *m_pLblHelpInfo;        // 帮助信息

    // 数据
    QString m_vehPlate;            // 车牌号
    int m_vehType;                 // 车型
    CTransPayType m_payType;       // 支付方式
    int m_amount;                  // 补费金额（分）
    RepayType m_repayType;         // 补费类型
    
    // 定时器
    QTimer *m_pAutoCloseTimer;     // 自动关闭定时器
    
    // 样式
    QFont m_fontTitle;
    QFont m_fontLabel;
    QFont m_fontValue;
    QFont m_fontHelp;
    QColor m_colorBackground;
    QColor m_colorSuccess;
};

#endif // FORMREPAYCOMPLETE_H


