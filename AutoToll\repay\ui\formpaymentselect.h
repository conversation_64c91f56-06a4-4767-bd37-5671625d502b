#ifndef FORMPAYMENTSELECT_H
#define FORMPAYMENTSELECT_H

#include <QLabel>
#include <QPushButton>
#include <QPainter>
#include <QKeyEvent>
#include <QVBoxLayout>
#include <QHBoxLayout>

#include "../../baseopwidget.h"
#include "../../MtcKey/MtcKeyDef.h"
#include "../../../common/lanetype.h"

/**
 * @brief 支付方式选择界面
 * 专门用于补费操作的支付方式选择
 * 使用数字键1、2、3...进行选择
 */
class FormPaymentSelect : public CBaseOpWidget
{
    Q_OBJECT

public:
    explicit FormPaymentSelect(QWidget *parent = 0);
    ~FormPaymentSelect();

    // 初始化界面
    virtual void InitUI(int iFlag = 0);
    
    // 显示支付方式选择并返回选择结果
    bool ShowPaymentSelect(CTransPayType &selectedPayType);
    
    // 设置可用的支付方式
    void SetAvailablePayTypes(const QList<CTransPayType> &payTypes);

protected:
    // 重写绘制事件
    void paintEvent(QPaintEvent *event);
    
    // 重写按键事件处理
    virtual int mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent);



private:
    // 初始化方法
    void InitUIConfig();
    void InitControls();
    void SetupControlProperties();
    void InitLayout();
    void InitConnections();
    
    // 界面更新
    void UpdatePaymentDisplay();
    void UpdateSelectionHighlight();
    void RefreshDisplay();
	void ClearOptionLabels();
	void LayoutOptionLabels();
    
    // 输入处理
    void ProcessNumberSelection(int number);
    void ProcessEscapeKey();
    void ProcessConfirmKey();
    
    // 选择处理
    void SelectPaymentType(int index);
    void CompleteSelection();
    void CancelSelection();
    
    // 辅助方法
    QString GetPayTypeName(CTransPayType payType);
    QString GetPayTypeDescription(CTransPayType payType);
    bool IsValidSelection(int index);

private:
    // 界面控件
    QLabel *m_pLblTitle;                    // 标题标签
    QLabel *m_pLblHelpInfo;                 // 帮助信息标签
	QList<QLabel*> m_optionLabels;          // 支付选项标签列表
    // 布局
    QVBoxLayout *m_pMainLayout;             // 主垂直布局
    QVBoxLayout *m_pOptionsLayout;          // 中部选项垂直布局（用于居中）

    // 界面状态
    QList<CTransPayType> m_availablePayTypes;  // 可用的支付方式
    CTransPayType m_selectedPayType;           // 选择的支付方式
    int m_selectedIndex;                       // 当前选中的索引
    bool m_bSelectionMade;                     // 是否已做出选择
    
    
    
    // 界面配置
    QFont m_fontTitle;                         // 标题字体
    QFont m_fontOption;                        // 选项字体
    QFont m_fontHelp;                          // 帮助字体
    QColor m_colorBackground;                  // 背景色
    QColor m_colorTitle;                       // 标题色
    QColor m_colorOption;                      // 选项色
    QColor m_colorSelected;                    // 选中色
    QColor m_colorHelp;                        // 帮助色
    
    // 常量
    static const int MAX_PAYMENT_TYPES = 10;     // 最大支付方式数量
    static const int OPTION_HEIGHT = 50;         // 选项高度
    static const int MARGIN = 20;                // 边距
};

#endif // FORMPAYMENTSELECT_H