#include "forminputvehtype.h"
#include <QPainter>
#include <QKeyEvent>
#include "../../globalui.h"
#include "../../dlgmain.h"
#include "../../log4qt/ilogmsg.h"

FormInputVehType::FormInputVehType(QWidget *parent)
    : CBaseOpWidget(parent)
    , m_pLblTitle(0)
    , m_pLblVehTypeLabel(0)
    , m_pLblVehTypeResult(0)
    , m_pLblHelpInfo(0)
    , m_inputVehType(1)
    , m_nCurrentPage(0)
    , m_nItemsPerPage(10)
    , m_nTotalPages(0)
{
    CreateControls();
    SetupControlProperties();
    InitConnections();
    InitVehTypeList();

    filterChildrenKeyEvent();
    setObjectName("FormInputVehType");
}

FormInputVehType::~FormInputVehType()
{
    // Qt会自动清理子控件
}

//void FormInputVehType::CreateControls()
//{
//    // 创建界面控件
//    m_pLblTitle = new QLabel(QString::fromUtf8("补费车型"), this);
//    m_pLblVehTypeLabel = new QLabel(QString::fromUtf8("车型"), this);
//    m_pLblVehTypeResult = new QLabel(QString::fromUtf8("客1"), this);  // 车型输入结果显示控件
//    m_pLblHelpInfo = new QLabel(this);
//}

void FormInputVehType::CreateControls()
{
    // 原有
    m_pLblTitle = new QLabel(QString::fromUtf8("补费车型"), this);
    m_pLblVehTypeLabel = new QLabel(QString::fromUtf8("车型："), this);
    m_pLblVehTypeResult = new QLabel(QString::fromUtf8("客1"), this);
    m_pLblHelpInfo = new QLabel(this);

    // 新增：车型候选区
    m_pChoicesWidget = new QWidget(this);
    m_pChoicesLayout = new QGridLayout(m_pChoicesWidget);
    m_pChoicesLayout->setSpacing(15);
    m_pChoicesLayout->setContentsMargins(10, 10, 10, 10);

    // 初始化10个候选项 (0-9)
    QStringList vehTypes;
    vehTypes << "0.客1" << "1.客2" << "2.客3" << "3.客4" << "4.货1"
             << "5.货2" << "6.货3" << "7.货4" << "8.货5" << "9.货6";

    for (int i = 0; i < vehTypes.size(); ++i) {
        QLabel *lbl = new QLabel(vehTypes[i], this);
        lbl->setAlignment(Qt::AlignCenter);
        lbl->setFrameShape(QFrame::Box);
        lbl->setStyleSheet("background-color: white; border: 1px solid gray; padding: 5px;");
        m_choiceLabels.append(lbl);
        m_pChoicesLayout->addWidget(lbl, i / 5, i % 5); // 每行5个
    }
}


void FormInputVehType::SetupControlProperties()
{
    // 设置字体
    m_fontTitle = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TitleFontSize);
    m_fontLabel = QFont(g_GlobalUI.m_FontName, 18);
    m_fontHelp = QFont(g_GlobalUI.m_FontName, 12);
    
    // 设置背景色
    m_colorBackground = g_GlobalUI.m_ColorBackground;
    
    // 标题
    m_pLblTitle->setFont(m_fontTitle);
    m_pLblTitle->setAlignment(Qt::AlignCenter);
    
    // 车型标签
    m_pLblVehTypeLabel->setFont(m_fontLabel);
    m_pLblVehTypeLabel->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
    
    // 车型输入结果显示
    m_pLblVehTypeResult->setFont(m_fontLabel);
    m_pLblVehTypeResult->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    m_pLblVehTypeResult->setStyleSheet("background-color: white; border: 2px solid gray; padding: 5px;");
    m_pLblVehTypeResult->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Fixed);
    m_pLblVehTypeResult->setFixedWidth(100);
    
    // 底部提示信息
    m_pLblHelpInfo->setFont(m_fontHelp);
    m_pLblHelpInfo->setAlignment(Qt::AlignCenter);
    m_pLblHelpInfo->setWordWrap(true);
    m_pLblHelpInfo->setStyleSheet("color: rgb(100, 100, 100);");
    m_pLblHelpInfo->setText(QString::fromUtf8("按【0-9】数字键选择车型，按【↑↓】翻页\n按【确认】键继续，按【ESC】键取消"));
    m_pLblHelpInfo->show();


    for (int i = 0; i < m_choiceLabels.size(); ++i) {
        m_choiceLabels[i]->setFont(m_fontLabel);
        m_choiceLabels[i]->setVisible(false);
    }

}

//void FormInputVehType::InitLayout()
//{
//    int width = rect().width();
//    int height = rect().height();
    
//    // 标题
//    m_pLblTitle->setGeometry(0, 10, width, 40);
    
//    // 主要布局区域
//    int centerY = height / 2;
//    int centerX = width / 2;
    
//    // 一行显示：标签 + 结果框
//    int labelWidth = 80;
//    int labelHeight = 30;
//    int resultWidth = 220;
//    int resultHeight = 50;
//    int spacing = 12;
//    int totalWidth = labelWidth + spacing + resultWidth;
//    int startX = centerX - totalWidth / 2;
//    int rowY = centerY - resultHeight / 2;

//    m_pLblVehTypeLabel->setGeometry(startX, rowY + (resultHeight - labelHeight) / 2, labelWidth, labelHeight);
//    m_pLblVehTypeResult->setGeometry(startX + labelWidth + spacing, rowY, resultWidth, resultHeight);
    
//    // 底部提示信息
//    m_pLblHelpInfo->setGeometry(20, height - 80, width - 40, 50);
//}

void FormInputVehType::InitLayout()
{
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(20, 20, 20, 20);
    mainLayout->setSpacing(20);

    // 标题
    m_pLblTitle->setFixedHeight(40);
    mainLayout->addWidget(m_pLblTitle, 0, Qt::AlignCenter);

    // 中间：标签 + 结果框
    QHBoxLayout *vehTypeLayout = new QHBoxLayout;
    vehTypeLayout->setSpacing(15);
    vehTypeLayout->addStretch();
    vehTypeLayout->addWidget(m_pLblVehTypeLabel, 0, Qt::AlignRight | Qt::AlignVCenter);
    vehTypeLayout->addWidget(m_pLblVehTypeResult, 0, Qt::AlignLeft | Qt::AlignVCenter);
    vehTypeLayout->addStretch();
    mainLayout->addLayout(vehTypeLayout);

    // 候选车型（网格）
    mainLayout->addWidget(m_pChoicesWidget, 0, Qt::AlignCenter);

    // 底部提示信息
    mainLayout->addStretch();
    mainLayout->addWidget(m_pLblHelpInfo, 0, Qt::AlignCenter);

    setLayout(mainLayout);
}




void FormInputVehType::InitConnections()
{
    // 目前不需要信号连接
}

bool FormInputVehType::InputVehType(int currentVehType)
{
    // 保存参数
    m_inputVehType = currentVehType;
    
    // 初始化界面
    InitUI();
    InitLayout();

    // 重置翻页状态
    ResetPagination();
    UpdatePagination();

    // 更新车型显示
    OnVehTypeChanged();

    // 显示模态对话框
    return (CBaseOpWidget::Rlt_OK == doModalShow());
}

void FormInputVehType::InitUI()
{
    CBaseOpWidget::InitUI();
}

int FormInputVehType::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    if (!mtcKeyEvent) return 0;

    // 处理数字键输入
    if (mtcKeyEvent->isNumKey()) {
        mtcKeyEvent->setKeyType(KC_Number);
        OnInputNumber(mtcKeyEvent->key());
        return 1;
    }

    // 处理功能键
    mtcKeyEvent->setKeyType(KC_Func);

    if (mtcKeyEvent->func() == KeyUp) {
        // 上翻页
        PrevPage();
    } else if (mtcKeyEvent->func() == KeyDown) {
        // 下翻页
        NextPage();
    } else if (mtcKeyEvent->func() == KeyConfirm) {
        OnConfirmClicked();
    } else if (mtcKeyEvent->func() == KeyEsc) {
        OnCancelClicked();
    } else if (mtcKeyEvent->func() == KeyDel) {
        OnClearInput();
    }

    return 1;
}

void FormInputVehType::OnInputVehType(int vehType)
{
    // 设置车型值
    m_inputVehType = vehType;

    // 更新显示
    OnVehTypeChanged();

    InfoLog(QString("车型输入：%1(%2)").arg(GetVehTypeName(vehType)).arg(vehType));
}

void FormInputVehType::OnInputNumber(int keyInput)
{
    // 数字键输入处理
    int nNum = keyInput - 48; // 转换为0-9
    if(--nNum < 0)  nNum = 9;
    int nActualIndex = m_nCurrentPage * m_nItemsPerPage + nNum;

    if (nActualIndex < m_vehTypeList.size()) {
        int selectedVehType = m_vehTypeList.at(nActualIndex);
        OnInputVehType(selectedVehType);
        InfoLog(QString("通过数字键%1选择车型：%2(%3)").arg(nNum).arg(GetVehTypeName(selectedVehType)).arg(selectedVehType));
    }
}

void FormInputVehType::OnClearInput()
{
    // 清除输入，回到默认车型
    m_inputVehType = 1;
    OnVehTypeChanged();
}

void FormInputVehType::InitVehTypeList()
{
    // 初始化车型列表，去掉客5和客6
    m_vehTypeList.clear();

    // 客车：客1-客4
    m_vehTypeList.append(1);  // VC_Car1
    m_vehTypeList.append(2);  // VC_Car2
    m_vehTypeList.append(3);  // VC_Car3
    m_vehTypeList.append(4);  // VC_Car4

    // 货车：货1-货6
    m_vehTypeList.append(11); // VC_Truck1
    m_vehTypeList.append(12); // VC_Truck2
    m_vehTypeList.append(13); // VC_Truck3
    m_vehTypeList.append(14); // VC_Truck4
    m_vehTypeList.append(15); // VC_Truck5
    m_vehTypeList.append(16); // VC_Truck6

    // 专项作业车：YJ1-YJ6
    m_vehTypeList.append(21); // VC_YJ1
    m_vehTypeList.append(22); // VC_YJ2
    m_vehTypeList.append(23); // VC_YJ3
    m_vehTypeList.append(24); // VC_YJ4
    m_vehTypeList.append(25); // VC_YJ5
    m_vehTypeList.append(26); // VC_YJ6
}

void FormInputVehType::UpdatePagination()
{
    if (m_vehTypeList.size() > 0) {
        m_nTotalPages = (m_vehTypeList.size() + m_nItemsPerPage - 1) / m_nItemsPerPage;
        if (m_nCurrentPage >= m_nTotalPages) {
            m_nCurrentPage = m_nTotalPages - 1;
        }
        if (m_nCurrentPage < 0) {
            m_nCurrentPage = 0;
        }
    } else {
        m_nTotalPages = 0;
        m_nCurrentPage = 0;
    }
}

void FormInputVehType::NextPage()
{
    if (m_nCurrentPage < m_nTotalPages - 1) {
        m_nCurrentPage++;
        update(); // 触发重绘
        InfoLog(QString("翻到下一页：%1/%2").arg(m_nCurrentPage + 1).arg(m_nTotalPages));
    }
}

void FormInputVehType::PrevPage()
{
    if (m_nCurrentPage > 0) {
        m_nCurrentPage--;
        update(); // 触发重绘
        InfoLog(QString("翻到上一页：%1/%2").arg(m_nCurrentPage + 1).arg(m_nTotalPages));
    }
}

void FormInputVehType::ResetPagination()
{
    m_nCurrentPage = 0;
    m_nTotalPages = 0;
}

bool FormInputVehType::ValidateVehType(QString &errorMsg)
{
    // 检查车型是否有效
    if (m_inputVehType < 1) {
        errorMsg = QString::fromUtf8("请选择有效的车型");
        return false;
    }
    
    // 检查是否为支持的车型
    QString vehTypeName = GetVehTypeName(m_inputVehType);
    if (vehTypeName.startsWith("未知")) {
        errorMsg = QString::fromUtf8("不支持的车型，请重新选择");
        return false;
    }
    
    return true;
}

void FormInputVehType::OnVehTypeChanged()
{
    // 更新车型结果显示控件
    QString vehTypeName = GetVehTypeName(m_inputVehType);
    m_pLblVehTypeResult->setText(vehTypeName);
    InfoLog(QString("车型已选择：%1(%2)").arg(vehTypeName).arg(m_inputVehType));
}

void FormInputVehType::OnConfirmClicked()
{
    QString errorMsg;
    if (ValidateVehType(errorMsg)) {
        InfoLog(QString("车型输入完成：%1(%2)").arg(GetVehTypeName(m_inputVehType)).arg(m_inputVehType));
        OnOk();
    } else {
        ShowErrorMsg(errorMsg);
    }
}

void FormInputVehType::OnCancelClicked()
{
    InfoLog("车型输入取消");
    OnCancel();
}

void FormInputVehType::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event);

    QRect rectClient = this->rect();
    QPixmap pixmap(rectClient.width(), rectClient.height());
    QPainter painter(&pixmap);

    // 绘制背景
    painter.setBrush(m_colorBackground);
    painter.setPen(Qt::black);
    painter.drawRect(rectClient.x(), rectClient.y(), rectClient.width() - 1, rectClient.height() - 1);

    // 绘制车型选择提示框
    if (m_vehTypeList.size() > 0) {
        QFont fontSelect = QFont(g_GlobalUI.m_FontName, 14);
        painter.setFont(fontSelect);
        painter.setPen(Qt::black);

        // 计算提示框位置（在车型输入结果框下方）
        int beginX = rect().width()-380;
        int beginY = m_pLblVehTypeLabel->geometry().bottom() + 30;
        int width = 400;
        int height = 160; // 提示框高度

        QRect rctShow(beginX, beginY, width, height);

        // 绘制提示框背景
        painter.setBrush(QColor(240, 240, 240));
        painter.setPen(Qt::gray);
        painter.drawRect(rctShow);

        // 绘制车型选择内容
        QString sText="\n";
        int startIndex = m_nCurrentPage * m_nItemsPerPage;
        int endIndex = qMin(startIndex + m_nItemsPerPage, m_vehTypeList.size());

        for (int i = startIndex+1; i < endIndex+1; i++) {
            int displayIndex = i - startIndex;
            int vehType = m_vehTypeList[i-1];
            QString vehTypeName = GetVehTypeName(vehType);
            sText += QString("%1.%2   ").arg(int(displayIndex % 10)).arg(vehTypeName);
            if (displayIndex == 5) {
                sText += "\n";
            }
            if (displayIndex == 10) {
                break;
            }
        }

        // 显示分页信息
        if (m_nTotalPages > 1) {
            sText += QString("\n\n第%1/%2页").arg(m_nCurrentPage + 1).arg(m_nTotalPages);
        }

        // 绘制文本
        painter.setPen(Qt::black);
        QRect textRect = rctShow.adjusted(10, 10, -10, -10);
        painter.drawText(textRect, Qt::AlignLeft | Qt::AlignTop, sText);
    }

    painter.end();
    QPainter ptThis(this);
    ptThis.drawPixmap(rectClient, pixmap);
}

QString FormInputVehType::GetVehTypeName(int vehType)
{
    switch (vehType) {
        case 1: return "客一";
        case 2: return "客二";
        case 3: return "客三";
        case 4: return "客四";
        case 11: return "货一";
        case 12: return "货二";
        case 13: return "货三";
        case 14: return "货四";
        case 15: return "货五";
        case 16: return "货六";
        case 21: return "专项一";
        case 22: return "专项二";
        case 23: return "专项三";
        case 24: return "专项四";
        case 25: return "专项五";
        case 26: return "专项六";
        default: return QString("未知车型(%1)").arg(vehType);
    }
}

QString FormInputVehType::GetVehTypeDescription(int vehType)
{
    switch (vehType) {
        case 1: return "7座以下小客车";
        case 2: return "8-19座客车";
        case 3: return "20-39座客车";
        case 4: return "40座以上客车";
        case 11: return "2吨以下货车";
        case 12: return "2-5吨货车";
        case 13: return "5-10吨货车";
        case 14: return "10-15吨货车";
        case 15: return "15-25吨货车";
        case 16: return "25吨以上货车";
        case 21: return "一类专项作业车";
        case 22: return "二类专项作业车";
        case 23: return "三类专项作业车";
        case 24: return "四类专项作业车";
        case 25: return "五类专项作业车";
        case 26: return "六类专项作业车";
        default: return QString("未知车型描述");
    }
}
