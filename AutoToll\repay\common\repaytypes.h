#ifndef REPAYTYPES_H
#define REPAYTYPES_H

#include <QString>
#include <QDateTime>
#include <QList>
#include "../../prodebpayment.h"

/**
 * @brief 补费类型枚举
 */
enum RepayType
{
    RepayType_None = 0,      // 无补费
    RepayType_Current = 1,   // 当趟补费
    RepayType_Province = 2   // 省内名单补费
};

/**
 * @brief 补费阶段枚举
 */
enum RepayStage
{
    RepayStage_None = 0,        // 未开始
    RepayStage_Authorization,   // 授权验证阶段
    RepayStage_VehicleInput,    // 车辆信息输入阶段
    RepayStage_DebtQuery,       // 欠费查询阶段
    RepayStage_AmountConfirm,   // 金额确认阶段
    RepayStage_Payment,         // 支付处理阶段
    RepayStage_Complete         // 完成阶段
};

/**
 * @brief 拦截方式枚举
 */
enum InterceptType
{
    Intercept_Entry = 1,  // 入口拦截
    Intercept_Exit = 2    // 出口拦截
};



/**
 * @brief 补费操作记录
 */
struct RepayOperationRecord
{
    qint64 id;                    // 记录ID（自增）
    QString operatorId;           // 操作员ID
    QString operatorName;         // 操作员姓名
    int operationType;            // 补费类型
    QString vehPlate;             // 车牌号
    int vehPlateColor;            // 车牌颜色
    int vehType;                  // 车型
    int repayAmount;              // 补费金额（分）
    int paymentType;              // 支付方式
    QString orderIds;             // 工单号（省内名单补费）
    QString listno;               // 查询编号（省内名单补费）
    QDateTime operationTime;      // 操作时间
    int result;                   // 处理结果：1-成功，0-失败
    QString errorMsg;             // 错误信息
    QString wasteId;              // 流水ID
    QString remark;               // 备注
    
    RepayOperationRecord()
    {
        id = 0;
        operationType = RepayType_None;
        vehPlateColor = 0;
        vehType = 0;
        repayAmount = 0;
        paymentType = 0;
        operationTime = QDateTime::currentDateTime();
        result = 0;
    }
};

/**
 * @brief 补费追缴明细结构体
 */
struct RepayDebtItem
{
    QString orderIds;        // 订单ID列表
    QString debtDate;        // 欠费日期
    QString entryStation;    // 入口站点
    QString exitStation;     // 出口站点
    int debtAmount;          // 欠费金额(分)
    QString passId;          // 通行ID
    QString remark;          // 备注信息
    
    RepayDebtItem()
    {
        debtAmount = 0;
    }
};

/**
 * @brief 补费追缴查询结果结构体
 */
struct RepayDebtQueryResult
{
    QString listno;                    // 查询编号
    QString vehiclePlate;              // 车牌号
    int vehiclePlateColor;             // 车牌颜色
    int totalAmount;                   // 总欠费金额(分)
    QList<RepayDebtItem> debtItems;    // 欠费明细列表
    QString queryTime;                 // 查询时间
    
    RepayDebtQueryResult()
    {
        vehiclePlateColor = 0;
        totalAmount = 0;
    }
};

/**
 * @brief 补费追缴请求结构体
 */
struct RepayDebtRequest
{
    QString requestId;      // 请求ID
    QString vlp;           // 车牌号
    int vlpc;              // 车牌颜色
    QString oweFee;        // 追缴金额
    QString axlecount;     // 轴数
    QString vehicletype;   // 收费车型
    QString station;       // 收费站编码
    QString laneid;        // 车道编号
    QString operator_id;   // 操作员
    QString listno;        // 查询编号
    
    RepayDebtRequest()
    {
        vlpc = 0;
    }
};

/**
 * @brief 授权记录
 */
struct AuthorizationRecord
{
    qint64 id;                    // 记录ID（自增）
    QString operatorId;           // 操作员ID
    int authType;                 // 授权类型：1-补费授权
    QDateTime authTime;           // 授权时间
    int result;                   // 授权结果：1-成功，0-失败
    QString clientInfo;           // 客户端信息
    QString remark;               // 备注
    
    AuthorizationRecord()
    {
        id = 0;
        authType = 1;
        authTime = QDateTime::currentDateTime();
        result = 0;
    }
};

/**
 * @brief 获取补费类型描述
 */
inline QString GetRepayTypeName(RepayType type)
{
    switch (type) {
        case RepayType_Current:
            return QString("当趟补费");
        case RepayType_Province:
            return QString("省内名单补费");
        default:
            return QString("未知类型");
    }
}

/**
 * @brief 获取补费阶段描述
 */
inline QString GetRepayStageName(RepayStage stage)
{
    switch (stage) {
        case RepayStage_Authorization:
            return QString("授权验证");
        case RepayStage_VehicleInput:
            return QString("车辆信息输入");
        case RepayStage_DebtQuery:
            return QString("欠费查询");
        case RepayStage_AmountConfirm:
            return QString("金额确认");
        case RepayStage_Payment:
            return QString("支付处理");
        case RepayStage_Complete:
            return QString("补费完成");
        default:
            return QString("未知阶段");
    }
}

/**
 * @brief 补费错误码
 */
enum RepayErrorCode
{
    RepayError_None = 0,                  // 无错误/成功
    RepayError_AuthFailed = 1001,         // 授权失败
    RepayError_VehicleInfoInvalid = 1002, // 车辆信息无效
    RepayError_AmountExceeded = 1003,     // 金额超限
    RepayError_VehicleNotFound = 1004,    // 车辆未找到
    RepayError_PaymentFailed = 1005,      // 支付失败
    RepayError_NetworkError = 1006,       // 网络错误
    RepayError_DatabaseError = 1007,      // 数据库错误
    RepayError_ConfigError = 1008,        // 配置错误
    RepayError_SystemError = 1009,        // 系统错误
    RepayError_DebtQueryFailed = 1010,    // 欠费查询失败
    RepayError_NetworkFailed = 1011,      // 网络失败
    RepayError_UserCancelled = 1012       // 用户取消
};

/**
 * @brief 获取补费错误描述
 */
inline QString GetRepayErrorMessage(RepayErrorCode errorCode)
{
    switch (errorCode) {
        case RepayError_None:
            return QString("操作成功");
        case RepayError_AuthFailed:
            return QString("授权验证失败");
        case RepayError_VehicleInfoInvalid:
            return QString("车辆信息无效");
        case RepayError_AmountExceeded:
            return QString("补费金额超出限制");
        case RepayError_VehicleNotFound:
            return QString("车辆未找到");
        case RepayError_DebtQueryFailed:
            return QString("欠费查询失败");
        case RepayError_PaymentFailed:
            return QString("支付处理失败");
        case RepayError_NetworkError:
            return QString("网络连接错误");
        case RepayError_NetworkFailed:
            return QString("网络连接失败");
        case RepayError_DatabaseError:
            return QString("数据库操作错误");
        case RepayError_UserCancelled:
            return QString("用户取消操作");
        case RepayError_ConfigError:
            return QString("配置参数错误");
        case RepayError_SystemError:
            return QString("系统内部错误");
        default:
            return QString("未知错误");
    }
}

#endif // REPAYTYPES_H