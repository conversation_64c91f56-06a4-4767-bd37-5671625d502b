#include "repay_db_manager.h"
#include <QSqlDatabase>
#include <QSqlQuery>
#include <QSqlError>
#include "../../log4qt/ilogmsg.h"  // 使用log4qt日志系统
#include <QDir>
#include <QDateTime>
#include <QCoreApplication>

RepayDbManager* RepayDbManager::m_pInstance = 0;
QMutex RepayDbManager::m_mutex;

RepayDbManager::RepayDbManager(QObject *parent)
    : QObject(parent)
    , m_bInitialized(false)
{
}

RepayDbManager* RepayDbManager::GetInstance()
{
    if (m_pInstance == 0) {
        QMutexLocker locker(&m_mutex);
        if (m_pInstance == 0) {
            m_pInstance = new RepayDbManager();
        }
    }
    return m_pInstance;
}

bool RepayDbManager::Initialize(const QString &dbPath)
{
    if (m_bInitialized) {
        return true;
    }
    
    m_sDbPath = dbPath;
    if (m_sDbPath.isEmpty()) {
        m_sDbPath = QCoreApplication::applicationDirPath() + "/lane.db";
    }
    
    // 创建数据库连接
    QSqlDatabase db = QSqlDatabase::addDatabase("QSQLITE", "RepayConnection");
    db.setDatabaseName(m_sDbPath);
    
    if (!db.open()) {
        ErrorLog(QString("打开补费数据库失败：%1").arg(db.lastError().text()));
        return false;
    }
    
    // 执行初始化脚本
    if (!CreateTables()) {
        ErrorLog("创建补费数据表失败");
        return false;
    }
    
    m_bInitialized = true;
    DebugLog("补费数据库管理器初始化成功");
    return true;
}

bool RepayDbManager::CreateTables()
{
    QSqlDatabase db = QSqlDatabase::database("RepayConnection");
    if (!db.isOpen()) {
        ErrorLog("数据库未打开");
        return false;
    }
    
    QSqlQuery query(db);
    
    // 创建补费操作日志表
    QString createRepayLogTable = 
        "CREATE TABLE IF NOT EXISTS RepayOperationLog ("
        "    id INTEGER PRIMARY KEY AUTOINCREMENT,"
        "    operatorId VARCHAR(20) NOT NULL,"
        "    operationType INTEGER NOT NULL,"
        "    vehPlate VARCHAR(20) NOT NULL,"
        "    vehPlateColor INTEGER NOT NULL,"
        "    repayAmount INTEGER NOT NULL,"
        "    paymentType INTEGER NOT NULL,"
        "    listno VARCHAR(50),"
        "    orderIds TEXT,"
        "    wasteId VARCHAR(50),"
        "    operationTime DATETIME NOT NULL,"
        "    result INTEGER NOT NULL,"
        "    remark TEXT"
        ")";
    
    if (!query.exec(createRepayLogTable)) {
        ErrorLog(QString("创建补费操作日志表失败：%1").arg(query.lastError().text()));
        return false;
    }
    
    // 创建授权操作日志表
    QString createAuthLogTable = 
        "CREATE TABLE IF NOT EXISTS AuthorizationLog ("
        "    id INTEGER PRIMARY KEY AUTOINCREMENT,"
        "    operatorId VARCHAR(20) NOT NULL,"
        "    authType INTEGER NOT NULL,"
        "    authTime DATETIME NOT NULL,"
        "    result INTEGER NOT NULL,"
        "    clientInfo VARCHAR(100),"
        "    remark TEXT"
        ")";
    
    if (!query.exec(createAuthLogTable)) {
        ErrorLog(QString("创建授权日志表失败：%1").arg(query.lastError().text()));
        return false;
    }
    
    // 创建索引
    if (!CreateIndexes()) {
        ErrorLog("创建索引失败");
        return false;
    }
    
    return true;
}

bool RepayDbManager::CreateIndexes()
{
    QSqlDatabase db = QSqlDatabase::database("RepayConnection");
    QSqlQuery query(db);
    
    QStringList indexes;
    indexes << "CREATE INDEX IF NOT EXISTS idx_repay_log_operator_time ON RepayOperationLog(operatorId, operationTime DESC)"
            << "CREATE INDEX IF NOT EXISTS idx_repay_log_vehicle ON RepayOperationLog(vehPlate, vehPlateColor)"
            << "CREATE INDEX IF NOT EXISTS idx_repay_log_time ON RepayOperationLog(operationTime)"
            << "CREATE INDEX IF NOT EXISTS idx_repay_log_waste ON RepayOperationLog(wasteId)"
            << "CREATE INDEX IF NOT EXISTS idx_auth_log_operator_time ON AuthorizationLog(operatorId, authTime DESC)"
            << "CREATE INDEX IF NOT EXISTS idx_auth_log_time ON AuthorizationLog(authTime)";
    
    foreach (const QString &sql, indexes) {
        if (!query.exec(sql)) {
            ErrorLog(QString("创建索引失败：%1").arg(query.lastError().text()));
            return false;
        }
    }
    
    return true;
}

bool RepayDbManager::LogRepayOperation(const RepayOperationRecord &record)
{
    QSqlDatabase db = QSqlDatabase::database("RepayConnection");
    if (!db.isOpen()) {
        ErrorLog("数据库未打开");
        return false;
    }
    
    QSqlQuery query(db);
    query.prepare(
        "INSERT INTO RepayOperationLog "
        "(operatorId, operationType, vehPlate, vehPlateColor, repayAmount, paymentType, "
        " listno, orderIds, wasteId, operationTime, result, remark) "
        "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    query.addBindValue(record.operatorId);
    query.addBindValue(record.operationType);
    query.addBindValue(record.vehPlate);
    query.addBindValue(record.vehPlateColor);
    query.addBindValue(record.repayAmount);
    query.addBindValue(record.paymentType);
    query.addBindValue(record.listno);
    query.addBindValue(record.orderIds);
    query.addBindValue(record.wasteId);
    query.addBindValue(record.operationTime);
    query.addBindValue(record.result);
    query.addBindValue(record.remark);
    
    if (!query.exec()) {
        ErrorLog(QString("记录补费操作失败：%1").arg(query.lastError().text()));
        return false;
    }
    
    return true;
}

bool RepayDbManager::LogAuthorization(const AuthorizationRecord &record)
{
    QSqlDatabase db = QSqlDatabase::database("RepayConnection");
    if (!db.isOpen()) {
        ErrorLog("数据库未打开");
        return false;
    }
    
    QSqlQuery query(db);
    query.prepare(
        "INSERT INTO AuthorizationLog "
        "(operatorId, authType, authTime, result, clientInfo, remark) "
        "VALUES (?, ?, ?, ?, ?, ?)");
    
    query.addBindValue(record.operatorId);
    query.addBindValue(record.authType);
    query.addBindValue(record.authTime);
    query.addBindValue(record.result);
    query.addBindValue(record.clientInfo);
    query.addBindValue(record.remark);
    
    if (!query.exec()) {
        ErrorLog(QString("记录授权操作失败：%1").arg(query.lastError().text()));
        return false;
    }
    
    return true;
}

QList<RepayOperationRecord> RepayDbManager::QueryRepayOperations(
    const QString &operatorId, 
    const QDateTime &startTime, 
    const QDateTime &endTime,
    int limit)
{
    QList<RepayOperationRecord> records;
    
    QSqlDatabase db = QSqlDatabase::database("RepayConnection");
    if (!db.isOpen()) {
        ErrorLog("数据库未打开");
        return records;
    }
    
    QSqlQuery query(db);
    QString sql = 
        "SELECT operatorId, operationType, vehPlate, vehPlateColor, repayAmount, paymentType, "
        "       listno, orderIds, wasteId, operationTime, result, remark "
        "FROM RepayOperationLog "
        "WHERE 1=1";
    
    if (!operatorId.isEmpty()) {
        sql += " AND operatorId = ?";
    }
    if (startTime.isValid()) {
        sql += " AND operationTime >= ?";
    }
    if (endTime.isValid()) {
        sql += " AND operationTime <= ?";
    }
    
    sql += " ORDER BY operationTime DESC";
    
    if (limit > 0) {
        sql += QString(" LIMIT %1").arg(limit);
    }
    
    query.prepare(sql);
    
    int paramIndex = 0;
    if (!operatorId.isEmpty()) {
        query.bindValue(paramIndex++, operatorId);
    }
    if (startTime.isValid()) {
        query.bindValue(paramIndex++, startTime);
    }
    if (endTime.isValid()) {
        query.bindValue(paramIndex++, endTime);
    }
    
    if (!query.exec()) {
        ErrorLog(QString("查询补费操作失败：%1").arg(query.lastError().text()));
        return records;
    }
    
    while (query.next()) {
        RepayOperationRecord record;
        record.operatorId = query.value(0).toString();
        record.operationType = query.value(1).toInt();
        record.vehPlate = query.value(2).toString();
        record.vehPlateColor = query.value(3).toInt();
        record.repayAmount = query.value(4).toInt();
        record.paymentType = query.value(5).toInt();
        record.listno = query.value(6).toString();
        record.orderIds = query.value(7).toString();
        record.wasteId = query.value(8).toString();
        record.operationTime = query.value(9).toDateTime();
        record.result = query.value(10).toInt();
        record.remark = query.value(11).toString();
        
        records.append(record);
    }
    
    return records;
}

bool RepayDbManager::CleanupOldLogs(int retentionDays)
{
    QSqlDatabase db = QSqlDatabase::database("RepayConnection");
    if (!db.isOpen()) {
        ErrorLog("数据库未打开");
        return false;
    }
    
    QDateTime cutoffTime = QDateTime::currentDateTime().addDays(-retentionDays);
    
    QSqlQuery query(db);
    
    // 清理补费操作日志
    query.prepare("DELETE FROM RepayOperationLog WHERE operationTime < ?");
    query.addBindValue(cutoffTime);
    
    if (!query.exec()) {
        ErrorLog(QString("清理补费操作日志失败：%1").arg(query.lastError().text()));
        return false;
    }
    
    int repayLogsDeleted = query.numRowsAffected();
    
    // 清理授权日志
    query.prepare("DELETE FROM AuthorizationLog WHERE authTime < ?");
    query.addBindValue(cutoffTime);
    
    if (!query.exec()) {
        ErrorLog(QString("清理授权日志失败：%1").arg(query.lastError().text()));
        return false;
    }
    
    int authLogsDeleted = query.numRowsAffected();
    
        InfoLog(QString("已清理 %1 条补费日志和 %2 条授权日志")
             .arg(repayLogsDeleted).arg(authLogsDeleted));
    
    return true;
}

bool RepayDbManager::OptimizeDatabase()
{
    QSqlDatabase db = QSqlDatabase::database("RepayConnection");
    if (!db.isOpen()) {
        ErrorLog("数据库未打开");
        return false;
    }
    
    QSqlQuery query(db);
    
    // 执行VACUUM以优化数据库
    if (!query.exec("VACUUM")) {
        ErrorLog(QString("压缩数据库失败：%1").arg(query.lastError().text()));
        return false;
    }
    
    // 分析数据库以优化查询计划
    if (!query.exec("ANALYZE")) {
        ErrorLog(QString("分析数据库失败：%1").arg(query.lastError().text()));
        return false;
    }
    
    InfoLog("数据库优化成功");
    return true;
}

bool RepayDbManager::BackupDatabase(const QString &backupPath)
{
    // 简单的数据库备份（复制文件）
    QString sourcePath = m_sDbPath;
    QString targetPath = backupPath;
    
    if (targetPath.isEmpty()) {
        QString timestamp = QDateTime::currentDateTime().toString("yyyyMMdd_HHmmss");
        targetPath = QCoreApplication::applicationDirPath() + 
                    QString("/backup/lane_backup_%1.db").arg(timestamp);
    }
    
    // 确保备份目录存在
    QDir dir;
    dir.mkpath(QFileInfo(targetPath).path());
    
    // 复制数据库文件
    if (!QFile::copy(sourcePath, targetPath)) {
        ErrorLog(QString("备份数据库失败，目标路径：%1").arg(targetPath));
        return false;
    }
    
    InfoLog(QString("数据库已备份到：%1").arg(targetPath));
    return true;
}

RepayDbManager::~RepayDbManager()
{
    if (QSqlDatabase::contains("RepayConnection")) {
        QSqlDatabase::removeDatabase("RepayConnection");
    }
} 