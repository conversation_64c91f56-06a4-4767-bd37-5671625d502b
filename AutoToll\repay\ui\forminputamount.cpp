#include "forminputamount.h"
#include <QPainter>
#include <QKeyEvent>
#include <QDoubleValidator>
#include "../../globalui.h"
#include "../../dlgmain.h"
#include "../../common/vehplatefunc.h"
#include "../config/repayconfig.h"
#include "../../common/paramfilemgr.h"
#include "../../common/parafileold.h"

FormInputAmount::FormInputAmount(QWidget *parent)
    : CBaseOpWidget(parent)
    , m_pLblTitle(0)
    , m_pLblVehPlate(0)
    , m_pLblVehPlateValue(0)
    , m_pLblVehType(0)
    , m_pLblVehTypeValue(0)
    , m_pLblMaxFeeLabel(0)
    , m_pLblMaxFeeValue(0)
    , m_pLblAmountLabel(0)
    , m_pEditAmount(0)
    , m_pLblAmountUnit(0)
    , m_pLblHelpInfo(0)
    , m_inputAmount(0)
    , m_vehPlateColor(1)
    , m_vehType(1)
    , m_maxFeeAmount(76000)
{
    CreateControls();
    SetupControlProperties();
    InitConnections();
    
    filterChildrenKeyEvent();
    setObjectName("FormInputAmount");
}

FormInputAmount::~FormInputAmount()
{
    // Qt会自动清理子控件
}

void FormInputAmount::CreateControls()
{
    // 创建界面控件
    m_pLblTitle = new QLabel(QString::fromUtf8("【当趟补费】补费金额"), this);

    // 车牌与车型：拆分为“标签 + 内容”
    m_pLblVehPlate = new QLabel(QString::fromUtf8("车    牌："), this);
    m_pLblVehPlateValue = new QLabel(this);

    m_pLblVehType = new QLabel(QString::fromUtf8("车    型："), this);
    m_pLblVehTypeValue = new QLabel(this);

    m_pLblMaxFeeLabel = new QLabel(QString::fromUtf8("最大金额："), this);
    m_pLblMaxFeeValue = new QLabel(this);
    m_pLblAmountLabel = new QLabel(QString::fromUtf8("补费金额："), this);
    m_pEditAmount = new QLineEdit(this);
    m_pLblAmountUnit = new QLabel(QString::fromUtf8("元"), this);
    m_pLblHelpInfo = new QLabel(this);
}

void FormInputAmount::SetupControlProperties()
{
    // 设置字体
    m_fontTitle = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TitleFontSize);
    // 参考期望样式放大字号
    m_fontLabel = QFont(QString("黑体"), 20);
    m_fontEdit = QFont(QString("仿宋"), 20);
    m_fontHelp = QFont(g_GlobalUI.m_FontName, 14);

    // 设置背景色
    m_colorBackground = g_GlobalUI.m_ColorBackground;
    
    // 标题
    m_pLblTitle->setFont(m_fontTitle);
    m_pLblTitle->setAlignment(Qt::AlignCenter);
    
    // 车牌信息（标签+内容）
    m_pLblVehPlate->setFont(m_fontLabel);
    m_pLblVehPlate->setAlignment(Qt::AlignCenter | Qt::AlignVCenter);
    if (m_pLblVehPlateValue) {
        m_pLblVehPlateValue->setFont(m_fontEdit);
        m_pLblVehPlateValue->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    }

    // 车型信息（标签+内容）
    m_pLblVehType->setFont(m_fontLabel);
    m_pLblVehType->setAlignment(Qt::AlignCenter | Qt::AlignVCenter);
    if (m_pLblVehTypeValue) {
        m_pLblVehTypeValue->setFont(m_fontEdit);
        m_pLblVehTypeValue->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    }

    // 最大收费金额标签
    m_pLblMaxFeeLabel->setFont(m_fontLabel);
    m_pLblMaxFeeLabel->setAlignment(Qt::AlignCenter | Qt::AlignVCenter);
    
    // 最大收费金额值
    m_pLblMaxFeeValue->setFont(m_fontEdit);
    m_pLblMaxFeeValue->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    
    // 补费金额标签
    m_pLblAmountLabel->setFont(m_fontLabel);
    m_pLblAmountLabel->setAlignment(Qt::AlignCenter | Qt::AlignVCenter);
    
    // 金额输入框
    m_pEditAmount->setFont(m_fontEdit);
    m_pEditAmount->setAlignment(Qt::AlignCenter);
    m_pEditAmount->setPlaceholderText("");
    // 数值校验与样式，允许输入小数点，限制小数点后最多2位
    QDoubleValidator *validator = new QDoubleValidator(0.00, 99999.99, 2, m_pEditAmount);
    validator->setNotation(QDoubleValidator::StandardNotation);
    m_pEditAmount->setValidator(validator);
    m_pEditAmount->setStyleSheet(
        "QLineEdit{border:2px solid #C0C0C0; border-radius:4px; background:white; padding:2px 6px;}"
    );

    // 金额单位
    m_pLblAmountUnit->setFont(m_fontEdit);
    m_pLblAmountUnit->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    
    // 帮助信息
    m_pLblHelpInfo->setFont(m_fontHelp);
    m_pLblHelpInfo->setAlignment(Qt::AlignCenter);
    m_pLblHelpInfo->setWordWrap(false);
    m_pLblHelpInfo->setStyleSheet("color: rgb(100, 100, 100);");
    m_pLblHelpInfo->setText(QString::fromUtf8("请输入金额（单位：元，2位小数）按【确认】完成，按【ESC】退出"));
}

//void FormInputAmount::InitLayout()
//{
//    int width = rect().width();
//    int height = rect().height();
    
//    // 标题
//    m_pLblTitle->setGeometry(0, 10, width, 40);
    
//    // 车牌信息行 - 左对齐显示
//    int leftMargin = 150;
//    int rowHeight = 45;
//    int currentY = 80;
    
//    int labelColWidth = 120;
//    int valueColWidth = 300;

//    // 车牌信息行（标签 + 内容）
//    m_pLblVehPlate->setGeometry(leftMargin, currentY, labelColWidth, rowHeight);
//    if (m_pLblVehPlateValue) m_pLblVehPlateValue->setGeometry(leftMargin + labelColWidth, currentY, valueColWidth, rowHeight);
//    currentY += rowHeight + 10;

//    // 车型信息行（标签 + 内容）
//    m_pLblVehType->setGeometry(leftMargin, currentY, labelColWidth, rowHeight);
//    if (m_pLblVehTypeValue) m_pLblVehTypeValue->setGeometry(leftMargin + labelColWidth, currentY, valueColWidth, rowHeight);
//    currentY += rowHeight + 10;

//    // 最大收费金额行
//    m_pLblMaxFeeLabel->setGeometry(leftMargin, currentY, 200, rowHeight);
//    m_pLblMaxFeeValue->setGeometry(leftMargin + 200, currentY, 200, rowHeight);
//    currentY += rowHeight + 20;
    
//    // 补费金额输入行
//    int labelWidth = 140;
//    int editWidth = 220;
//    int unitWidth = 40;

//    m_pLblAmountLabel->setGeometry(leftMargin, currentY, labelWidth, rowHeight);
//    m_pEditAmount->setGeometry(leftMargin + labelWidth + 20, currentY, editWidth, rowHeight);
//    m_pLblAmountUnit->setGeometry(leftMargin + labelWidth + 20 + editWidth + 10, currentY, unitWidth, rowHeight);

//    // 帮助信息 - 底部居中
//    m_pLblHelpInfo->setGeometry(0, height - 60, width, 30);
//}
void FormInputAmount::InitLayout()
{
    int width = rect().width();
    int height = rect().height();

    // 标题
    m_pLblTitle->setGeometry(0, 10, width, 40);

    // 行高
    int rowHeight = 45;
    int currentY = 80;

    // 用字体测量“最大金额”的宽度
    QFontMetrics fm(m_pLblMaxFeeLabel->font());
    int maxFeeLabelWidth = fm.width(QString::fromUtf8("最最大金额额"));
    int spacing = 20;
    int valueWidth = 300;
    int unitWidth = 40;

    // 总宽度 = label宽度 + spacing + value宽度
    int totalWidth = maxFeeLabelWidth + spacing + valueWidth;
    int startX = (width - totalWidth) / 2;

    // 车牌信息
    m_pLblVehPlate->setGeometry(startX, currentY, maxFeeLabelWidth, rowHeight);
    m_pLblVehPlateValue->setGeometry(startX + maxFeeLabelWidth + spacing, currentY, valueWidth, rowHeight);
    currentY += rowHeight + 10;

    // 车型信息
    m_pLblVehType->setGeometry(startX, currentY, maxFeeLabelWidth, rowHeight);
    m_pLblVehTypeValue->setGeometry(startX + maxFeeLabelWidth + spacing, currentY, valueWidth, rowHeight);
    currentY += rowHeight + 10;

    // 最大金额
    m_pLblMaxFeeLabel->setGeometry(startX, currentY, maxFeeLabelWidth, rowHeight);
    m_pLblMaxFeeValue->setGeometry(startX + maxFeeLabelWidth + spacing, currentY, valueWidth, rowHeight);
    currentY += rowHeight + 20;

    // 补费金额
    m_pLblAmountLabel->setGeometry(startX, currentY, maxFeeLabelWidth, rowHeight);
    m_pEditAmount->setGeometry(startX + maxFeeLabelWidth + spacing, currentY, valueWidth, rowHeight);
    m_pLblAmountUnit->setGeometry(startX + maxFeeLabelWidth + spacing + valueWidth + spacing, currentY, unitWidth, rowHeight);

    // 底部提示信息
    m_pLblHelpInfo->setGeometry(0, height - 60, width, 30);
}



void FormInputAmount::InitConnections()
{
    connect(m_pEditAmount, SIGNAL(textChanged(QString)), this, SLOT(OnAmountChanged()));
}

bool FormInputAmount::InputAmount(int currentAmount, const QString &vehPlate, int vehPlateColor, int vehType)
{
    // 保存参数
    m_inputAmount = currentAmount;
    m_vehPlate = vehPlate;
    m_vehPlateColor = vehPlateColor;
    m_vehType = vehType;
    
    // 直接从参数表查询当前车型的最大收费金额
    m_maxFeeAmount = GetMaxFeeFromSpParaTable(vehType);
    if (m_maxFeeAmount <= 0) {
        // 如果参数表查询失败，使用默认值
        m_maxFeeAmount = GetDefaultMaxFeeByVehType(vehType);
        WarnLog(QString("参数表查询失败，使用默认最大收费金额：车型=%1，金额=%2分").arg(vehType).arg(m_maxFeeAmount));
    } else {
        InfoLog(QString("从参数表获取最大收费金额：车型=%1，金额=%2分").arg(vehType).arg(m_maxFeeAmount));
    }
    
    // 初始化界面
    InitUI();
    InitLayout();
    
    // 设置车辆信息显示（标签 + 内容）
    m_pLblVehPlate->setText(QString::fromUtf8("车 牌："));
    if (m_pLblVehPlateValue) m_pLblVehPlateValue->setText(vehPlate);

    // 设置车型信息显示（标签 + 内容）
    QString vehTypeName = GetVehTypeName(vehType);
    m_pLblVehType->setText(QString::fromUtf8("车 型："));
    if (m_pLblVehTypeValue) m_pLblVehTypeValue->setText(vehTypeName);

    // 设置最大收费金额显示（转换为元显示）
    m_pLblMaxFeeValue->setText(QString("%1 元").arg(QString::number(m_maxFeeAmount / 100.0, 'f', 2)));

    // 设置当前金额（将分转换为元显示）
    if (currentAmount > 0) {
        double amountInYuan = currentAmount / 100.0;
        m_inputText = QString::number(amountInYuan, 'f', 2);
        m_pEditAmount->setText(m_inputText);
    } else {
        m_inputText = QString("0.00");
        m_pEditAmount->setText(m_inputText);
        m_pEditAmount->selectAll();
    }

    // 设置焦点
    m_pEditAmount->setFocus();

    // 显示模态对话框
    return (CBaseOpWidget::Rlt_OK == doModalShow());
}

void FormInputAmount::InitUI()
{
    CBaseOpWidget::InitUI();
}

int FormInputAmount::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    if (!mtcKeyEvent) return 0;

    // 处理数字键
    if (mtcKeyEvent->isNumKey()) {
        mtcKeyEvent->setKeyType(KC_Number);
        OnInputNumber(mtcKeyEvent->key());
        return 1;
    }

    // 处理功能键
    mtcKeyEvent->setKeyType(KC_Func);
    if (mtcKeyEvent->func() == KeyConfirm) {
        OnConfirmClicked();
    } else if (mtcKeyEvent->func() == KeyEsc) {
        OnCancelClicked();
    } else if (mtcKeyEvent->func() == KeyDel) {
        OnDeleteInput();
    } else if (mtcKeyEvent->func() == KeyPoint) {
        OnInputDecimalPoint();
    }

    return 1;
}

void FormInputAmount::OnInputNumber(int keyInput)
{
    // 添加数字到输入文本
    char ch = static_cast<char>(keyInput);
    if (ch >= '0' && ch <= '9') {
        // 检查小数点后是否已有2位数字
        int dotIndex = m_inputText.indexOf('.');
        if (dotIndex >= 0 && m_inputText.length() - dotIndex > 2) {
            // 小数点后已有2位数字，不允许再输入
            return;
        }
        m_inputText += ch;
        m_pEditAmount->setText(m_inputText);
    }
}

void FormInputAmount::OnInputDecimalPoint()
{
    // 添加小数点，但只允许一个小数点
    if (!m_inputText.contains('.')) {
        if (m_inputText.isEmpty()) {
            m_inputText = "0.";
        } else {
            m_inputText += '.';
        }
        m_pEditAmount->setText(m_inputText);
    }
}

void FormInputAmount::OnDeleteInput()
{
    // 删除最后一个字符
    if (!m_inputText.isEmpty()) {
        m_inputText.chop(1);
        m_pEditAmount->setText(m_inputText);
    }
}

bool FormInputAmount::ValidateAmount(QString &errorMsg)
{
    if (m_inputText.isEmpty()) {
        errorMsg = QString::fromUtf8("请输入补费金额");
        return false;
    }

    bool ok;
    double amountInYuan = m_inputText.toDouble(&ok);
    if (!ok || amountInYuan <= 0) {
        errorMsg = QString::fromUtf8("请输入有效的金额");
        return false;
    }

    // 将元转换为分进行比较
    int amountInCents = static_cast<int>(amountInYuan * 100 + 0.5); // 四舍五入

    // 检查是否超过最大收费金额（都转换为分进行比较）
    if (amountInCents > m_maxFeeAmount) {
        errorMsg = QString::fromUtf8("补费金额不能超过最大收费金额 %1 元")
                   .arg(QString::number(m_maxFeeAmount / 100.0, 'f', 2));
        return false;
    }

    // 检查是否超过9999.99元
    if (amountInYuan > 9999.99) {
        errorMsg = QString::fromUtf8("金额不能超过9999.99元");
        return false;
    }

    return true;
}

void FormInputAmount::OnAmountChanged()
{
    // 实时验证输入
    QString text = m_pEditAmount->text();
    bool ok;
    double amountInYuan = text.toDouble(&ok);

    if (ok && amountInYuan >= 0) {
        // 将元转换为分存储
        m_inputAmount = static_cast<int>(amountInYuan * 100 + 0.5); // 四舍五入
        m_pEditAmount->setStyleSheet("");
    } else {
        m_pEditAmount->setStyleSheet("border: 1px solid red;");
    }
}

void FormInputAmount::OnConfirmClicked()
{
    QString errorMsg;
    if (ValidateAmount(errorMsg)) {
        // 保存最终金额
        bool ok;
        double amountInYuan = m_inputText.toDouble(&ok);
        if (ok) {
            // 将元转换为分存储
            m_inputAmount = static_cast<int>(amountInYuan * 100 + 0.5); // 四舍五入
            OnOk();
        }
    } else {
        ShowErrorMsg(errorMsg);
    }
}

void FormInputAmount::OnCancelClicked()
{
    OnCancel();
}

void FormInputAmount::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event);
    
    QRect rectClient = this->rect();
    QPixmap pixmap(rectClient.width(), rectClient.height());
    QPainter painter(&pixmap);

    // 绘制背景
    painter.setBrush(m_colorBackground);
    painter.setPen(Qt::black);
    painter.drawRect(rectClient.x(), rectClient.y(), rectClient.width() - 1, rectClient.height() - 1);

    painter.end();
    QPainter ptThis(this);
    ptThis.drawPixmap(rectClient, pixmap);
}

QString FormInputAmount::GetVehTypeName(int vehType)
{
    switch (vehType) {
        case 1: return "客1";
        case 2: return "客2";
        case 3: return "客3";
        case 4: return "客4";
        case 11: return "货1";
        case 12: return "货2";
        case 13: return "货3";
        case 14: return "货4";
        case 15: return "货5";
        case 16: return "货6";
        default: return QString("类型%1").arg(vehType);
    }
}

int FormInputAmount::GetDefaultMaxFeeByVehType(int vehType)
{
    // 返回默认的最大收费金额（分）
    switch (vehType) {
        case 1: return 76000;  // 客1：760元
        case 2: return 114000; // 客2：1140元
        case 3: return 190000; // 客3：1900元
        case 4: return 285000; // 客4：2850元
        case 11: return 76000; // 货1：760元
        case 12: return 152000; // 货2：1520元
        case 13: return 304000; // 货3：3040元
        case 14: return 456000; // 货4：4560元
        case 15: return 608000; // 货5：6080元
        case 16: return 760000; // 货6：7600元
        default: return 76000; // 默认760元
    }
}

int FormInputAmount::GetMaxFeeFromSpParaTable(int vehType)
{
    // 从特殊参数表查询最大收费金额（特殊参数106）
    SpParaTable *pTable = (SpParaTable *)CParamFileMgr::GetParamFile(cfSpPara);
    if (!pTable) {
        WarnLog("特殊参数表未加载");
        return 0;
    }
    
    quint32 nMaxFee = 0;
    double baseFee = 0;
    if (pTable->QryMaxFee(vehType, nMaxFee, baseFee)) {
        return static_cast<int>(nMaxFee);
    } else {
        WarnLog(QString("特殊参数表查询失败：车型=%1").arg(vehType));
        return 0;
    }
}
