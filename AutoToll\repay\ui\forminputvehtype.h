#ifndef FORMINPUTVEHTYPE_H
#define FORMINPUTVEHTYPE_H

#include <QWidget>
#include <QLabel>
#include "baseopwidget.h"

/**
 * @brief 车型输入界面
 * 专门用于输入车型的独立页面，仿造FormInputPlate实现翻页选择
 */
class FormInputVehType : public CBaseOpWidget
{
    Q_OBJECT

public:
    explicit FormInputVehType(QWidget *parent = 0);
    ~FormInputVehType();

    /**
     * @brief 显示车型输入界面
     * @param currentVehType 当前车型
     * @return true-用户确认输入，false-用户取消
     */
    bool InputVehType(int currentVehType = 1);

    /**
     * @brief 获取输入的车型
     * @return 车型值
     */
    int GetInputVehType() const { return m_inputVehType; }

protected:
    // 界面初始化
    void InitUI();
    void CreateControls();
    void SetupControlProperties();
    void InitLayout();
    void InitConnections();

    // 事件处理
    int mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent);
    void paintEvent(QPaintEvent *event);

    // 输入处理
    void OnInputVehType(int vehType);
    void OnInputNumber(int keyInput);
    void OnClearInput();

    // 验证
    bool ValidateVehType(QString &errorMsg);

    // 翻页相关方法
    void UpdatePagination();      // 更新翻页信息
    void NextPage();              // 下一页
    void PrevPage();              // 上一页
    void ResetPagination();       // 重置翻页状态
    void InitVehTypeList();       // 初始化车型列表

private slots:
    void OnVehTypeChanged();
    void OnConfirmClicked();
    void OnCancelClicked();

private:
    // 辅助方法
    QString GetVehTypeName(int vehType);
    QString GetVehTypeDescription(int vehType);

private:
    // 界面控件
    QLabel *m_pLblTitle;           // 标题
    QLabel *m_pLblVehTypeLabel;    // 车型标签
    QLabel *m_pLblVehTypeResult;   // 车型输入结果显示
    QLabel *m_pLblHelpInfo;        // 帮助信息

    QWidget *m_pChoicesWidget;
    QGridLayout *m_pChoicesLayout;
    QVector<QLabel*> m_choiceLabels;

    // 数据
    int m_inputVehType;            // 输入的车型

    // 车型选择相关
    QList<int> m_vehTypeList;      // 可选车型列表
    int m_nCurrentPage;            // 当前页码（从0开始）
    int m_nItemsPerPage;           // 每页显示的项目数量
    int m_nTotalPages;             // 总页数

    // 样式
    QFont m_fontTitle;
    QFont m_fontLabel;
    QFont m_fontHelp;
    QColor m_colorBackground;
};

#endif // FORMINPUTVEHTYPE_H
